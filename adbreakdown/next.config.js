/** @type {import('next').NextConfig} */
const nextConfig = {
    experimental: {
        reactCompiler: true,
    },
    // Exclude supabase functions from build
    webpack: (config) => {
        config.externals = config.externals || [];
        config.externals.push({
            'https://deno.land/std@0.177.0/http/server.ts': 'commonjs https://deno.land/std@0.177.0/http/server.ts',
        });
        return config;
    },
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'i.ytimg.com',
            },
            {
                protocol: 'https',
                hostname: 'img.youtube.com',
            },
            {
                protocol: 'https',
                hostname: 'placehold.co',
            }
        ],
    },
    // Enable static exports for truly static pages
    output: process.env.NEXT_OUTPUT === 'export' ? 'export' : undefined,
    // Improve caching with static assets
    async headers() {
        return [
            {
                source: '/(.*)',
                headers: [
                    {
                        key: 'X-Content-Type-Options',
                        value: 'nosniff',
                    },
                    {
                        key: 'X-Frame-Options',
                        value: 'DENY',
                    },
                    {
                        key: 'X-XSS-Protection',
                        value: '1; mode=block',
                    },
                ],
            },
            // Static assets caching
            {
                source: '/(.*)\\.(ico|png|jpg|jpeg|gif|webp|svg)$',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=31536000, immutable',
                    },
                ],
            },
            // Font caching
            {
                source: '/(.*)\\.(woff|woff2|eot|ttf|otf)$',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=31536000, immutable',
                    },
                ],
            },
            // CSS and JS caching
            {
                source: '/(.*)\\.(css|js)$',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=31536000, immutable',
                    },
                ],
            },
        ]
    },
};

module.exports = nextConfig;

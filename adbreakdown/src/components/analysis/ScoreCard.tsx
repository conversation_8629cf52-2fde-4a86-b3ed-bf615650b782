'use client'

import React from 'react'
import { LucideIcon } from 'lucide-react'

interface ScoreCardProps {
  label: string
  score: number
  icon: LucideIcon
  color?: 'blue' | 'purple' | 'red' | 'yellow' | 'green' | 'indigo'
}

const ScoreCard = ({ label, score, icon: Icon, color = "blue" }: ScoreCardProps) => {
  const colorConfig = {
    blue: {
      icon: 'text-blue-600',
      text: 'text-blue-600', 
      bg: 'bg-blue-500'
    },
    purple: {
      icon: 'text-purple-600',
      text: 'text-purple-600',
      bg: 'bg-purple-500'
    },
    red: {
      icon: 'text-red-600', 
      text: 'text-red-600',
      bg: 'bg-red-500'
    },
    yellow: {
      icon: 'text-yellow-600',
      text: 'text-yellow-600', 
      bg: 'bg-yellow-500'
    },
    green: {
      icon: 'text-green-600',
      text: 'text-green-600',
      bg: 'bg-green-500'
    },
    indigo: {
      icon: 'text-indigo-600',
      text: 'text-indigo-600',
      bg: 'bg-indigo-500'
    }
  }

  const currentColor = colorConfig[color]

  return (
    <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-2">
        <Icon className={`w-5 h-5 ${currentColor.icon}`} />
        <span className={`text-2xl font-bold ${currentColor.text}`}>{score}</span>
      </div>
      <p className="text-sm text-gray-600 font-medium">{label}</p>
      <div className="mt-2 bg-gray-200 rounded-full h-2">
        <div 
          className={`${currentColor.bg} h-2 rounded-full transition-all duration-500`}
          style={{ width: `${score * 10}%` }}
        />
      </div>
    </div>
  )
}

export default ScoreCard
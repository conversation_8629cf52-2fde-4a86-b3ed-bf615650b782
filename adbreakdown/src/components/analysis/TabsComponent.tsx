'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  TrendingUp, Users, Target, Lightbulb
} from 'lucide-react'

interface TabsComponentProps {
  parsedData: any
}

export default function TabsComponent({ parsedData }: TabsComponentProps) {
  if (!parsedData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Analysis data not available</p>
      </div>
    )
  }

  return (
    <Tabs defaultValue="audience-market" className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="audience-market" className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          Audience & Market
        </TabsTrigger>
        <TabsTrigger value="brand-strategy" className="flex items-center gap-2">
          <Target className="w-4 h-4" />
          Brand & Strategy
        </TabsTrigger>
        <TabsTrigger value="creative-culture" className="flex items-center gap-2">
          <Lightbulb className="w-4 h-4" />
          Creative & Culture
        </TabsTrigger>
        <TabsTrigger value="patterns-predictions" className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          Patterns & Predictions
        </TabsTrigger>
      </TabsList>
      
      {/* Tab 1: Audience & Market */}
      <TabsContent value="audience-market" className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Target Audience */}
          {parsedData.strategic_deep_dive?.brand_and_market_context?.target_audience_evidence && (
            <Card className="border-blue-200">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-800">
                  <Users className="w-5 h-5 mr-2" />
                  Target Audience
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <p className="text-blue-800 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_and_market_context.target_audience_evidence}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Competitive Landscape */}
          {parsedData.strategic_deep_dive?.brand_and_market_context?.competitive_landscape_evidence && (
            <Card className="border-green-200">
              <CardHeader>
                <CardTitle className="flex items-center text-green-800">
                  <Target className="w-5 h-5 mr-2" />
                  Competitive Landscape
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <p className="text-green-800 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_and_market_context.competitive_landscape_evidence}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 2: Brand & Strategy */}
      <TabsContent value="brand-strategy" className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Brand Identity */}
          {parsedData.strategic_deep_dive?.brand_strategy && (
            <Card className="border-purple-200">
              <CardHeader>
                <CardTitle className="flex items-center text-purple-800">
                  <Target className="w-5 h-5 mr-2" />
                  Brand Identity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {parsedData.strategic_deep_dive.brand_strategy.brand_position && (
                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                      <h4 className="font-semibold text-purple-900 mb-2">Brand Position</h4>
                      <p className="text-purple-800 leading-relaxed">
                        {parsedData.strategic_deep_dive.brand_strategy.brand_position}
                      </p>
                    </div>
                  )}
                  {parsedData.strategic_deep_dive.brand_strategy.brand_archetype && (
                    <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                      <h4 className="font-semibold text-purple-900 mb-2">Brand Archetype</h4>
                      <p className="text-purple-800 leading-relaxed">
                        {parsedData.strategic_deep_dive.brand_strategy.brand_archetype}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* The Hypothetical Brief */}
          {parsedData.strategic_deep_dive?.brand_strategy?.hypothetical_brief && (
            <Card className="border-green-200">
              <CardHeader>
                <CardTitle className="flex items-center text-green-800">
                  <Lightbulb className="w-5 h-5 mr-2" />
                  The Hypothetical Brief
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <p className="text-green-800 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_strategy.hypothetical_brief}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 3: Creative & Culture */}
      <TabsContent value="creative-culture" className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* The Creative Game Plan */}
          {parsedData.strategic_deep_dive?.creative_and_cultural_context?.creative_game_plan && (
            <Card className="border-orange-200">
              <CardHeader>
                <CardTitle className="flex items-center text-orange-800">
                  <Lightbulb className="w-5 h-5 mr-2" />
                  The Creative Game Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                  <p className="text-orange-800 leading-relaxed">
                    {parsedData.strategic_deep_dive.creative_and_cultural_context.creative_game_plan}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* The Cultural Hook */}
          {parsedData.strategic_deep_dive?.creative_and_cultural_context?.cultural_hook && (
            <Card className="border-indigo-200">
              <CardHeader>
                <CardTitle className="flex items-center text-indigo-800">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  The Cultural Hook
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
                  <p className="text-indigo-800 leading-relaxed">
                    {parsedData.strategic_deep_dive.creative_and_cultural_context.cultural_hook}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 4: Patterns & Predictions */}
      <TabsContent value="patterns-predictions" className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Industry Patterns */}
          {parsedData.internal_signals?.pattern_recognition && (
            <Card className="border-blue-200">
              <CardHeader>
                <CardTitle className="flex items-center text-blue-800">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Industry Patterns
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <p className="text-blue-800 leading-relaxed">
                    {parsedData.internal_signals.pattern_recognition}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Performance Predictions */}
          {parsedData.internal_signals?.prediction_factors && (
            <Card className="border-green-200">
              <CardHeader>
                <CardTitle className="flex items-center text-green-800">
                  <Target className="w-5 h-5 mr-2" />
                  Performance Predictions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <p className="text-green-800 leading-relaxed">
                    {parsedData.internal_signals.prediction_factors}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>
    </Tabs>
  )
}
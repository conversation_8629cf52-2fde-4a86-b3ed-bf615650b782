'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'

interface ExecutiveBriefingCardProps {
  parsedData: any
}

export default function ExecutiveBriefingCard({ parsedData }: ExecutiveBriefingCardProps) {
  if (!parsedData?.executive_briefing) {
    return null
  }

  const { the_one_thing_that_matters, gut_reaction } = parsedData.executive_briefing

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardContent className="p-6">
        {/* Header with AI Expert */}
        <div className="flex items-center space-x-3 mb-6">
          <Image
            src="/Gemini_Generated_Image_bzln87bzln87bzln.png"
            alt="AI Expert Avatar"
            width={48}
            height={48}
            className="w-12 h-12 rounded-full object-cover"
          />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-1">The Expert&apos;s Verdict</h2>
            <p className="text-gray-600 text-sm">Professional marketing assessment</p>
          </div>
        </div>
        
        {/* The Bottom Line - Main Takeaway */}
        {the_one_thing_that_matters && (
          <div className="mb-4">
            <h3 className="text-base font-semibold text-gray-900 mb-3">The Bottom Line</h3>
            <div className="bg-white border border-gray-200 p-4 rounded-lg">
              <p className="text-gray-700 leading-relaxed">
                {the_one_thing_that_matters}
              </p>
            </div>
          </div>
        )}
        
        {/* Gut Reaction Quote */}
        {gut_reaction && (
          <div>
            <h3 className="text-base font-semibold text-gray-900 mb-3">Gut Reaction</h3>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <blockquote className="text-gray-700 italic leading-relaxed text-sm">
                &ldquo;{gut_reaction}&rdquo;
              </blockquote>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
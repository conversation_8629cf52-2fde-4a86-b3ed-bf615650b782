'use client'

import React from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Info } from 'lucide-react'

interface MetadataSidebarProps {
  parsedData: any
  youtubeMetadata?: any
  videoMetadata?: any
}

export default function MetadataSidebar({ parsedData, youtubeMetadata, videoMetadata }: MetadataSidebarProps) {
  const metadata = parsedData?.metadata

  if (!metadata) {
    // Fallback to existing metadata structure if available
    return (
      <Card className="shadow-md">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold text-gray-900 flex items-center">
            <Info className="w-5 h-5 mr-2 text-green-600" />
            Ad Details
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {videoMetadata?.inferredBrandName && (
              <div>
                <div className="text-sm font-medium text-gray-600">Brand</div>
                <div className="text-sm text-gray-900">{videoMetadata.inferredBrandName}</div>
              </div>
            )}
            {videoMetadata?.duration && (
              <div>
                <div className="text-sm font-medium text-gray-600">Duration</div>
                <div className="text-sm text-gray-900">{videoMetadata.duration}</div>
              </div>
            )}
            {youtubeMetadata?.channelTitle && (
              <div>
                <div className="text-sm font-medium text-gray-600">Channel</div>
                <div className="text-sm text-gray-900">{youtubeMetadata.channelTitle}</div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-semibold text-gray-900 flex items-center">
          <Info className="w-4 h-4 mr-2 text-gray-500" />
          Ad Details
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {metadata.ad_title && (
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">Title</div>
              <div className="text-sm text-gray-900 leading-relaxed">{metadata.ad_title}</div>
            </div>
          )}
          
          {metadata.brand && (
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">Brand</div>
              <div className="text-sm text-gray-900">{metadata.brand}</div>
            </div>
          )}
          
          {metadata.product_category && (
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">Category</div>
              <div className="text-sm text-gray-900">{metadata.product_category}</div>
            </div>
          )}
          
          {metadata.campaign_category && (
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">Campaign Type</div>
              <div className="text-sm text-gray-900">{metadata.campaign_category}</div>
            </div>
          )}
          
          {metadata.runtime && (
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">Runtime</div>
              <div className="text-sm text-gray-900">{metadata.runtime}</div>
            </div>
          )}
          
          {metadata.celebrity && metadata.celebrity !== 'None' && metadata.celebrity !== 'N/A' && (
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">Celebrity</div>
              <div className="text-sm text-gray-900">{metadata.celebrity}</div>
            </div>
          )}
          
          {metadata.geography && (
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">Geography</div>
              <div className="text-sm text-gray-900">{metadata.geography}</div>
            </div>
          )}
          
          {metadata.parent_entity && metadata.parent_entity !== metadata.brand && (
            <div>
              <div className="text-sm font-medium text-gray-600 mb-1">Parent Company</div>
              <div className="text-sm text-gray-900">{metadata.parent_entity}</div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
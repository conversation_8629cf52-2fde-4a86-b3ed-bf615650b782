'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { BarChart3 } from 'lucide-react'

interface ScorecardSidebarProps {
  parsedData: any
}

export default function ScorecardSidebar({ parsedData }: ScorecardSidebarProps) {
  const scorecard = parsedData?.executive_briefing?.scorecard

  if (!scorecard) {
    return null
  }

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-semibold text-gray-900 flex items-center">
          <BarChart3 className="w-4 h-4 mr-2 text-gray-500" />
          Performance Scorecard
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {/* Overall Score - Prominent Display */}
        {scorecard.overall_impact_score && (
          <div className="text-center mb-4 pb-4 border-b border-gray-200">
            <div className="text-3xl font-bold text-gray-900 mb-1">
              {scorecard.overall_impact_score.score}
              <span className="text-lg text-gray-500">/10</span>
            </div>
            <div className="text-xs font-medium text-gray-600 mb-2">Overall Impact</div>
            <p className="text-xs text-gray-500 leading-relaxed">
              {scorecard.overall_impact_score.justification}
            </p>
          </div>
        )}
        
        {/* Individual Scores */}
        <div className="space-y-3">
          {scorecard.brand_integration && (
            <div className="group relative">
              <div className="flex justify-between items-center py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors cursor-help">
                <span className="text-sm font-medium text-gray-700">Brand Integration</span>
                <div className="text-right">
                  <span className="text-base font-semibold text-gray-900">{scorecard.brand_integration.score}</span>
                  <span className="text-xs text-gray-500">/10</span>
                </div>
              </div>
              {/* Custom Tooltip */}
              <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 text-gray-700 text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 pointer-events-none shadow-lg">
                {scorecard.brand_integration.justification}
              </div>
            </div>
          )}
          
          {scorecard.emotional_response && (
            <div className="group relative">
              <div className="flex justify-between items-center py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors cursor-help">
                <span className="text-sm font-medium text-gray-700">Emotional Response</span>
                <div className="text-right">
                  <span className="text-base font-semibold text-gray-900">{scorecard.emotional_response.score}</span>
                  <span className="text-xs text-gray-500">/10</span>
                </div>
              </div>
              {/* Custom Tooltip */}
              <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 text-gray-700 text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 pointer-events-none shadow-lg">
                {scorecard.emotional_response.justification}
              </div>
            </div>
          )}
          
          {scorecard.hook_power && (
            <div className="group relative">
              <div className="flex justify-between items-center py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors cursor-help">
                <span className="text-sm font-medium text-gray-700">Hook Power</span>
                <div className="text-right">
                  <span className="text-base font-semibold text-gray-900">{scorecard.hook_power.score}</span>
                  <span className="text-xs text-gray-500">/10</span>
                </div>
              </div>
              {/* Custom Tooltip */}
              <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 text-gray-700 text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 pointer-events-none shadow-lg">
                {scorecard.hook_power.justification}
              </div>
            </div>
          )}
          
          {scorecard.brand_differentiation && (
            <div className="group relative">
              <div className="flex justify-between items-center py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors cursor-help">
                <span className="text-sm font-medium text-gray-700">Brand Differentiation</span>
                <div className="text-right">
                  <span className="text-base font-semibold text-gray-900">{scorecard.brand_differentiation.score}</span>
                  <span className="text-xs text-gray-500">/10</span>
                </div>
              </div>
              {/* Custom Tooltip */}
              <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 text-gray-700 text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 pointer-events-none shadow-lg">
                {scorecard.brand_differentiation.justification}
              </div>
            </div>
          )}
          
          {scorecard.brand_consideration && (
            <div className="group relative">
              <div className="flex justify-between items-center py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors cursor-help">
                <span className="text-sm font-medium text-gray-700">Brand Consideration</span>
                <div className="text-right">
                  <span className="text-base font-semibold text-gray-900">{scorecard.brand_consideration.score}</span>
                  <span className="text-xs text-gray-500">/10</span>
                </div>
              </div>
              {/* Custom Tooltip */}
              <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 text-gray-700 text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 pointer-events-none shadow-lg">
                {scorecard.brand_consideration.justification}
              </div>
            </div>
          )}
          
          {scorecard.purchase_intent && (
            <div className="group relative">
              <div className="flex justify-between items-center py-3 px-3 rounded-lg hover:bg-gray-50 transition-colors cursor-help">
                <span className="text-sm font-medium text-gray-700">Purchase Intent</span>
                <div className="text-right">
                  <span className="text-base font-semibold text-gray-900">{scorecard.purchase_intent.score}</span>
                  <span className="text-xs text-gray-500">/10</span>
                </div>
              </div>
              {/* Custom Tooltip */}
              <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 text-gray-700 text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 pointer-events-none shadow-lg">
                {scorecard.purchase_intent.justification}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
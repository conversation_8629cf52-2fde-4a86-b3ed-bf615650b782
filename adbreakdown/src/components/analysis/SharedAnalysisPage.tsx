'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Globe, Share2, TrendingUp, Users, Target, Award, Lightbulb, Brain, Trophy, AlertTriangle, Quote,
  MessageSquare, Monitor, Hash as HashIcon, Settings
} from 'lucide-react'
import Link from 'next/link'
import ProcessingStatusCard from '@/components/analysis/ProcessingStatusCard'
import VideoPlayer from '@/components/analysis/VideoPlayer'
import CampaignAnalysis from '@/components/analysis/CampaignAnalysis'
import { getMarketingAnalysisPrompt } from '@/lib/prompts/marketingAnalysisPrompt'
import { getMarketingCopyPrompt } from '@/lib/prompts/marketingCopyPrompt'
import { getSocialMediaPostsPrompt } from '@/lib/prompts/socialMediaPostsPrompt'
import { getMarketingScorecardPrompt } from '@/lib/prompts/marketingScorecardPrompt'
import { getSeoKeywordsPrompt } from '@/lib/prompts/seoKeywordsPrompt'
import { getContentSuggestionsPrompt } from '@/lib/prompts/contentSuggestionsPrompt'
import LoginOverlay from '@/components/ui/LoginOverlay'
import Navigation from '@/components/Navigation'
import DeleteAnalysisModal from '@/components/analysis/DeleteAnalysisModal'
import ShareAnalysisModal from '@/components/analysis/ShareAnalysisModal'
import LoadingSkeleton from '@/components/analysis/LoadingSkeleton'
import ErrorDisplay from '@/components/analysis/ErrorDisplay'
import GeneratedContentSections from '@/components/analysis/GeneratedContentSections'
import PerformanceScorecard from '@/components/analysis/PerformanceScorecard'
import OverallImpressionTestimonial from '@/components/analysis/OverallImpressionTestimonial'
import AnalysisInsightCard from '@/components/analysis/AnalysisInsightCard'

// Interfaces
interface VideoMetadata {
  title: string
  thumbnail: string
  duration: string
  inferredBrandName: string
}

interface YouTubeMetadata {
  title: string
  description: string
  channelTitle: string
  publishedAt: string
  viewCount: string
  likeCount: string
  commentCount: string
  tags: string[]
  categoryId: string
  defaultLanguage?: string
  duration: string
  definition: string
}

interface DetailedAnalysisData {
  overallSentiment: number
  emotions: Record<string, number>
  scriptAnalysis: {
    transcript: string
    keyThemes: string[]
    sentimentTimeline: Array<{ time: number; sentiment: number }>
  }
  visualAnalysis: {
    scenes: Array<{ time: number; description: string; objects: string[] }>
    colorPalette: string[]
    visualAppeal: number
  }
  audioAnalysis: {
    musicMood: string
    voiceTone: string
    audioQuality: number
    soundEffects: string[]
  }
  targetingRecommendations: {
    demographics: string[]
    interests: string[]
    behaviors: string[]
  }
  competitorComparison: Array<{ name: string; sentiment: number; effectiveness: number }>
}

interface SharedAnalysisPageProps {
  analysisId: string
  isFeaturedMode?: boolean
  preloadedAnalysis?: any
}

// Main Component
export default function SharedAnalysisPage({ 
  analysisId, 
  isFeaturedMode = false, 
  preloadedAnalysis = null 
}: SharedAnalysisPageProps) {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const { checkCredits, deductCredits, loading: creditsLoading } = useCredits()
  const [analysis, setAnalysis] = useState<any>(preloadedAnalysis)
  const [loading, setLoading] = useState(!preloadedAnalysis)
  const [error, setError] = useState('')
  const [showVideo, setShowVideo] = useState(false)
  const [youtubeVideoId, setYoutubeVideoId] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [youtubeMetadata, setYoutubeMetadata] = useState<YouTubeMetadata | null>(null)
  const [showLoginOverlay, setShowLoginOverlay] = useState(false)
  const [loadedTabs, setLoadedTabs] = useState<Set<string>>(new Set(['overview']))
  const [isInitialLoading, setIsInitialLoading] = useState(!preloadedAnalysis)

  // Core analysis data states (from prototype)
  const [videoInfo, setVideoInfo] = useState('') // Raw transcript + summary text
  const [marketingAnalysis, setMarketingAnalysis] = useState('') // Detailed 10-point analysis

  // Generated content states
  const [marketingCopy, setMarketingCopy] = useState('')
  const [socialMediaPosts, setSocialMediaPosts] = useState('')
  const [marketingScorecard, setMarketingScorecard] = useState('')
  const [seoKeywords, setSeoKeywords] = useState('')
  const [contentSuggestions, setContentSuggestions] = useState('')
  
  // Loading states for individual features
  const [copyLoading, setCopyLoading] = useState(false)
  const [socialLoading, setSocialLoading] = useState(false)
  const [scorecardLoading, setScorecardLoading] = useState(false)
  const [keywordsLoading, setKeywordsLoading] = useState(false)
  const [suggestionsLoading, setSuggestionsLoading] = useState(false)
  const [togglePublicLoading, setTogglePublicLoading] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)

  // Extract badges data from analysis
  const getBadges = () => {
    const badges = []
    
    // Try to parse marketing analysis for rich data
    let marketingData: any = null
    try {
      if (marketingAnalysis && typeof marketingAnalysis === 'string') {
        let cleanData = marketingAnalysis
          .replace(/```json\s*/g, '')
          .replace(/```\s*/g, '')
          .trim()
        
        const jsonMatch = cleanData.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          marketingData = JSON.parse(jsonMatch[0])
        }
      } else if (marketingAnalysis && typeof marketingAnalysis === 'object') {
        marketingData = marketingAnalysis
      }
    } catch (error) {
      console.log('Could not parse marketing analysis for badges')
    }
    
    // Brand name - prefer from marketing analysis, fallback to current analysis
    const brandName = marketingData?.lightning_round?.brand || analysis?.inferred_brand
    if (brandName) {
      badges.push({ label: brandName, variant: 'secondary' as const })
    }
    
    // Product category - prefer from marketing analysis, fallback to current analysis
    const productCategory = marketingData?.lightning_round?.product_category || analysis?.product_category
    if (productCategory) {
      badges.push({ label: productCategory, variant: 'secondary' as const })
    }
    
    // Parent entity - from marketing analysis
    const parentEntity = marketingData?.lightning_round?.parent_entity
    if (parentEntity) {
      badges.push({ label: parentEntity, variant: 'secondary' as const })
    }
    
    // Campaign category - prefer from marketing analysis, fallback to current analysis
    const campaignCategory = marketingData?.lightning_round?.campaign_category || analysis?.campaign_category
    if (campaignCategory) {
      badges.push({ label: campaignCategory, variant: 'secondary' as const })
    }
    
    // Runtime - from marketing analysis
    const runtime = marketingData?.lightning_round?.runtime
    if (runtime) {
      badges.push({ label: runtime, variant: 'secondary' as const })
    }
    
    // Celebrity - from marketing analysis AI extraction
    const celebrity = marketingData?.lightning_round?.celebrity
    if (celebrity && celebrity.trim() !== '' && celebrity.toLowerCase() !== 'none' && celebrity.toLowerCase() !== 'n/a') {
      // Handle multiple celebrities separated by commas or "and"
      const celebrityList = celebrity.split(/,|\band\b/i).map((name: string) => name.trim()).filter((name: string) => name.length > 0)
      
      celebrityList.forEach((name: string) => {
        badges.push({ label: name, variant: 'secondary' as const })
      })
    }
    
    return badges
  }

  // Data states
  const [videoMetadata, setVideoMetadata] = useState<VideoMetadata>({
    title: 'Loading analysis...',
    thumbnail: 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Loading...',
    duration: '0:00',
    inferredBrandName: 'Loading...'
  })
  
  // Removed unused detailedAnalysisData state

  const formatDuration = (seconds: number | null) => {
    if (seconds === null || seconds === undefined) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.round(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'hqdefault' | 'mqdefault' | 'sddefault' | 'maxresdefault' = 'maxresdefault'): string => {
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`
  }

  const fetchYouTubeMetadata = async (videoId: string) => {
    try {
      const API_KEY = process.env.NEXT_PUBLIC_YOUTUBE_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY
      if (!API_KEY) {
        console.warn('No YouTube API key available')
        return
      }

      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${API_KEY}&part=snippet,statistics,contentDetails`
      )
      
      if (!response.ok) {
        throw new Error('Failed to fetch YouTube metadata')
      }
      
      const data = await response.json()
      
      if (data.items && data.items.length > 0) {
        const video = data.items[0]
        const snippet = video.snippet
        const statistics = video.statistics
        const contentDetails = video.contentDetails
        
        // Parse ISO 8601 duration (PT4M13S -> 4:13)
        const parseDuration = (duration: string) => {
          const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
          if (!match) return '0:00'
          
          const hours = parseInt(match[1] || '0')
          const minutes = parseInt(match[2] || '0')
          const seconds = parseInt(match[3] || '0')
          
          if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
          }
          return `${minutes}:${seconds.toString().padStart(2, '0')}`
        }
        
        const metadata: YouTubeMetadata = {
          title: snippet.title || 'Untitled Video',
          description: snippet.description || 'No description available',
          channelTitle: snippet.channelTitle || 'Unknown Channel',
          publishedAt: snippet.publishedAt,
          viewCount: statistics.viewCount || '0',
          likeCount: statistics.likeCount || '0',
          commentCount: statistics.commentCount || '0',
          tags: snippet.tags || [],
          categoryId: snippet.categoryId || '',
          defaultLanguage: snippet.defaultLanguage,
          duration: parseDuration(contentDetails.duration),
          definition: contentDetails.definition || 'sd'
        }
        
        setYoutubeMetadata(metadata)
      }
    } catch (error) {
      console.error('Error fetching YouTube metadata:', error)
    }
  }

  const parseAndSetData = useCallback((analysisData: any) => {
    console.log('parseAndSetData: Received analysis data', analysisData)
    setAnalysis(analysisData)
    
    // Extract YouTube video ID from URL first
    let videoId: string | null = null
    if (analysisData.video_url) {
      videoId = extractYouTubeVideoId(analysisData.video_url)
      setYoutubeVideoId(videoId)
      
      // Fetch YouTube metadata
      if (videoId) {
        fetchYouTubeMetadata(videoId)
      }
    }
    
    // Also check if we have a direct youtube_video_id field
    if (analysisData.youtube_video_id) {
      setYoutubeVideoId(analysisData.youtube_video_id)
      
      // Fetch YouTube metadata if we don't already have it
      if (!videoId) {
        fetchYouTubeMetadata(analysisData.youtube_video_id)
      }
    }
    
    // Use YouTube thumbnail if we have a video ID, otherwise fallback to stored thumbnail
    const thumbnailUrl = videoId 
      ? getYouTubeThumbnail(videoId, 'maxresdefault')
      : analysisData.video_thumbnail_url || analysisData.thumbnail_url || 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail'
    
    setVideoMetadata({
      title: analysisData.video_title || analysisData.title || 'No Title',
      thumbnail: thumbnailUrl,
      duration: formatDuration(analysisData.video_duration || analysisData.duration_seconds),
      inferredBrandName: analysisData.inferred_brand || 'N/A'
    })

    // Set video info and marketing analysis from database (matching prototype structure)
    if (analysisData.video_info) {
      setVideoInfo(analysisData.video_info)
    } else if (analysisData.transcript && analysisData.summary) {
      setVideoInfo(analysisData.transcript + "\n\n**Summary:**\n" + analysisData.summary)
    }
    
    if (analysisData.marketing_analysis) {
      setMarketingAnalysis(analysisData.marketing_analysis)
    }

    // Set generated content from database
    if (analysisData.marketing_copy) setMarketingCopy(analysisData.marketing_copy)
    if (analysisData.social_media_posts) setSocialMediaPosts(analysisData.social_media_posts)
    if (analysisData.marketing_scorecard) setMarketingScorecard(analysisData.marketing_scorecard)
    if (analysisData.seo_keywords) setSeoKeywords(analysisData.seo_keywords)
    if (analysisData.content_suggestions) setContentSuggestions(analysisData.content_suggestions)
    // Enhanced script handling removed for simplicity

    // Simplified data handling - detailed analysis data removed for cleaner code

    // Populate other generated reports
    analysisData.reports?.forEach((report: any) => {
      if (report.status === 'generated') {
        const content = formatGeneratedContent(report.report_types.name, report.content)
        switch (report.report_types.name) {
          case 'marketing_copy': setMarketingCopy(content); break;
          case 'social_media_posts': setSocialMediaPosts(content); break;
          case 'marketing_scorecard': setMarketingScorecard(content); break;
          case 'seo_keywords': setSeoKeywords(content); break;
          case 'content_suggestions': setContentSuggestions(content); break;
          // Removed emotion_timeline and enhanced_script cases for simplicity
        }
      }
    })
  }, [])

  const fetchBasicAnalysis = useCallback(async () => {
    if (!analysisId) return
    setIsInitialLoading(true)
    setLoading(true)
    try {
      // First, fetch basic data quickly for above-the-fold content with cache-busting
      const timestamp = Date.now()
      const basicRes = await fetch(`/api/analyses/${analysisId}/basic?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!basicRes.ok) {
        const errData = await basicRes.json()
        throw new Error(errData.error || 'Failed to fetch analysis data.')
      }
      const basicData = await basicRes.json()
      console.log('fetchBasicAnalysis: Basic data fetched (cache-busted)', basicData)
      
      // Set basic data immediately for fast rendering
      setAnalysis(basicData)
      setIsInitialLoading(false)
      setLoading(false)
      
      // Then fetch full data in background with a small delay to improve perceived performance
      setTimeout(async () => {
        try {
          const fullTimestamp = Date.now()
          const fullRes = await fetch(`/api/analyses/${analysisId}?_t=${fullTimestamp}`, {
            cache: 'no-store',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          })
          if (fullRes.ok) {
            const fullData = await fullRes.json()
            parseAndSetData(fullData)
          }
        } catch (err) {
          console.warn('Failed to load full analysis data:', err)
        }
      }, 100)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setIsInitialLoading(false)
      setLoading(false)
    }
  }, [analysisId, parseAndSetData])

  const fetchAnalysis = useCallback(async () => {
    if (!analysisId) return
    setLoading(true)
    try {
      const timestamp = Date.now()
      const res = await fetch(`/api/analyses/${analysisId}?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!res.ok) {
        const errData = await res.json()
        throw new Error(errData.error || 'Failed to fetch analysis data.')
      }
      const data = await res.json()
      
      console.log('fetchAnalysis: Fresh data fetched (cache-busted)', data)
      
      parseAndSetData(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
    } finally {
      setLoading(false)
    }
  }, [analysisId, parseAndSetData])

  useEffect(() => {
    // Skip data fetching if we have preloaded analysis
    if (preloadedAnalysis) {
      parseAndSetData(preloadedAnalysis)
      return
    }

    // Use progressive loading when authenticated OR try loading public analysis
    if ((isAuthenticated && !authLoading) || (!authLoading && !isAuthenticated)) {
      fetchBasicAnalysis()
    }
  }, [isAuthenticated, authLoading, fetchBasicAnalysis, preloadedAnalysis, parseAndSetData])

  // Scroll detection for login overlay (only for non-featured mode)
  useEffect(() => {
    // Skip scroll listener for featured mode or authenticated users
    if (isFeaturedMode || isAuthenticated || !analysis?.is_public) {
      return
    }

    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const windowHeight = window.innerHeight
      
      // Show overlay when user scrolls past the second "fold" 
      // This is approximately after the video player and campaign analysis sections
      // Use a more conservative trigger point - 2 viewport heights or when user scrolls significantly
      const triggerPoint = Math.max(windowHeight * 1.2, 800) // Either 1.2 viewport heights or 800px minimum
      
      if (scrollPosition >= triggerPoint && !showLoginOverlay) {
        setShowLoginOverlay(true)
      }
    }

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true })
    
    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [isFeaturedMode, isAuthenticated, analysis?.is_public, showLoginOverlay])

  // Tab handling removed for simplified component

  const handleDeleteAnalysis = async () => {
    setIsDeleting(true)
    setError('')
    
    try {
      const response = await fetch(`/api/analyses/${analysisId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete analysis')
      }
      
      // Redirect to dashboard after successful deletion
      window.location.href = '/dashboard'
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete analysis')
      setIsDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  // Remove client-side Gemini API calls - these should be handled server-side

  const handleTriggerInitialAnalysis = useCallback(async () => {
    setLoading(true)
    setError('')
    
    // Check authentication and credits first
    if (!isAuthenticated) {
      setError('Please sign in to run analysis.')
      setLoading(false)
      return
    }
    
    if (!checkCredits(1)) {
      setError('Insufficient credits. Please upgrade your plan to continue.')
      setLoading(false)
      return
    }
    
    try {
      // Deduct credit first
      const creditDeducted = await deductCredits(1)
      if (!creditDeducted) {
        setError('Failed to process credit. Please try again.')
        setLoading(false)
        return
      }
      
      // First get the analysis record with cache-busting
      const timestamp = Date.now()
      const analysisRes = await fetch(`/api/analyses/${analysisId}?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!analysisRes.ok) {
        throw new Error('Failed to fetch analysis data.')
      }
      const analysisData = await analysisRes.json()
      
      if (!analysisData.video_url) {
        throw new Error('No video URL found for analysis.')
      }

      // Trigger the initial analysis via server-side API
      const triggerRes = await fetch(`/api/analyses/${analysisId}/trigger-initial-analysis`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!triggerRes.ok) {
        const errorData = await triggerRes.json()
        throw new Error(errorData.error || 'Failed to trigger AI analysis.')
      }

      // Refresh the data
      await fetchAnalysis()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setLoading(false)
    }
  }, [isAuthenticated, checkCredits, deductCredits, analysisId, fetchAnalysis])

  // Auto-trigger step 2 (marketing analysis) instead of step 1 
  useEffect(() => {
    if (analysis?.status === 'pending' && !authLoading && !loading && !creditsLoading && !isFeaturedMode) {
      if (isAuthenticated && checkCredits(1)) {
        // Add small delay to ensure page is fully loaded
        const timer = setTimeout(() => {
          handleTriggerInitialAnalysis()
        }, 1000)
        return () => clearTimeout(timer)
      }
    }
  }, [analysis?.status, isAuthenticated, authLoading, loading, creditsLoading, checkCredits, handleTriggerInitialAnalysis, isFeaturedMode])

  useEffect(() => {
    if (analysis?.title) {
      document.title = `${analysis.title} - AdBreakdown Analysis`
    } else if (youtubeMetadata?.title) {
      document.title = `${youtubeMetadata.title} - AdBreakdown Analysis`
    } else {
      document.title = 'Loading Analysis - AdBreakdown'
    }
  }, [analysis?.title, youtubeMetadata?.title])

  const generateFeature = async (featureType: string, setContent: Function, setLoadingState: Function) => {
    setLoadingState(true)
    setError('')
    
    // Check authentication and credits first
    if (!isAuthenticated) {
      setError('Please sign in to generate content.')
      setLoadingState(false)
      return
    }
    
    if (!checkCredits(1)) {
      setError('Insufficient credits. Please upgrade your plan to continue.')
      setLoadingState(false)
      return
    }
    
    try {
      // Deduct credit first
      const creditDeducted = await deductCredits(1)
      if (!creditDeducted) {
        setError('Failed to process credit. Please try again.')
        setLoadingState(false)
        return
      }
      
      // Use videoInfo and marketingAnalysis for context (matching prototype)
      if (!videoInfo || !marketingAnalysis) {
        throw new Error('Please complete the initial analysis first to get enough context.')
      }
      let generatedContent = ''
      let prompt = ''

      switch (featureType) {
        case 'marketing_copy':
          prompt = getMarketingCopyPrompt(videoInfo, marketingAnalysis)
          break
        case 'social_media_posts':
          prompt = getSocialMediaPostsPrompt(videoInfo, marketingAnalysis)
          break
        case 'marketing_scorecard':
          prompt = getMarketingScorecardPrompt(marketingAnalysis)
          break
        case 'seo_keywords':
          prompt = getSeoKeywordsPrompt(videoInfo)
          break
        case 'content_suggestions':
          prompt = getContentSuggestionsPrompt(marketingAnalysis)
          break
        default:
          throw new Error(`Unknown feature type: ${featureType}`)
      }

      // Use server-side API endpoint for LLM feature generation
      const generateRes = await fetch(`/api/analyses/${analysisId}/generate-llm-feature`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          featureType: featureType
        })
      })

      if (!generateRes.ok) {
        const errorData = await generateRes.json()
        throw new Error(errorData.error || 'Failed to generate content')
      }

      const result = await generateRes.json()
      generatedContent = result.content
      setContent(generatedContent)
      setLoadingState(false)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setLoadingState(false)
    }
  }

  // Emotion timeline and enhanced script generation removed for simplified component

  const togglePublicStatus = async () => {
    setTogglePublicLoading(true)
    setError('')

    if (!isAuthenticated) {
      setError('Please sign in to modify analysis visibility.')
      setTogglePublicLoading(false)
      return
    }

    try {
      const res = await fetch(`/api/analyses/${analysisId}/toggle-public`, {
        method: 'POST',
      })

      if (!res.ok) {
        const errData = await res.json()
        throw new Error(errData.error || 'Failed to update analysis visibility.')
      }

      const result = await res.json()
      
      // Update local state
      setAnalysis((prev: any) => prev ? { ...prev, is_public: result.analysis.is_public } : prev)
      
      setTogglePublicLoading(false)
      // Show success message briefly
      setTimeout(() => setError(''), 3000)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update visibility.')
      setTogglePublicLoading(false)
    }
  }

  const handleShare = () => {
    setShowShareModal(true)
  }

  const getShareUrl = () => {
    if (!analysis) return ''
    return `${window.location.origin}/ad/${analysis.slug || analysis.id}`
  }

  const getShareText = () => {
    if (!analysis) return ''
    const adTitle = analysis.video_title || analysis.title || `${analysis.inferred_brand || 'Brand'} Ad`
    return `Check out this AI analysis of "${adTitle}" on`
  }

  const shareToTwitter = () => {
    const text = encodeURIComponent(getShareText())
    const url = encodeURIComponent(getShareUrl())
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank')
  }

  const shareToLinkedIn = () => {
    const url = encodeURIComponent(getShareUrl())
    const title = encodeURIComponent(`${analysis?.inferred_brand || 'Brand'} Ad Analysis - AdBreakdown`)
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank')
  }

  const shareToWhatsApp = () => {
    const text = encodeURIComponent(`${getShareText()} ${getShareUrl()}`)
    window.open(`https://wa.me/?text=${text}`, '_blank')
  }

  const shareToInstagram = () => {
    // Instagram doesn't have a direct web sharing API, so we copy to clipboard and show instructions
    copyToClipboard()
    alert('Link copied! Open Instagram and paste the link in your story or post.')
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(getShareUrl())
      alert('Link copied to clipboard!')
    } catch (error) {
      console.error('Clipboard error:', error)
      // Fallback: show the URL to copy manually
      prompt('Copy this link:', getShareUrl())
    }
  }

  const formatGeneratedContent = (_: string, content: any): string => {
    if (!content) return 'No content generated.'
    // This function can be expanded to format different report types
    return content.raw_content || content.scorecard_table || JSON.stringify(content, null, 2)
  }

  // Render Logic
  if (authLoading) {
    return <div className="min-h-screen flex items-center justify-center"><p>Loading authentication...</p></div>
  }
  
  // Allow public access if analysis is public, otherwise require authentication
  const isTestingMode = process.env.NODE_ENV === 'development'
  const isPublicAnalysis = analysis?.is_public === true
  
  if (!isAuthenticated && !isTestingMode && !isPublicAnalysis && !isFeaturedMode) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-[calc(100vh-64px)]">
          <div className="text-center">
            <p className="mb-4">Please sign in to view this analysis.</p>
            <Link href="/sign-in"><Button>Sign In</Button></Link>
          </div>
        </div>
      </div>
    )
  }
  if (isInitialLoading) {
    return <LoadingSkeleton />
  }

  if (loading && !analysis) {
    return <div className="min-h-screen flex items-center justify-center"><p>Loading analysis data...</p></div>
  }

  // If analysis is null after loading, it means no analysis exists for this ID
  if (!analysis && !loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-[calc(100vh-64px)]">
          <Card className="mb-8 text-center">
            <CardHeader>
              <CardTitle>Analysis Not Found</CardTitle>
              <CardDescription>No analysis found for ID: {analysisId}.</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/dashboard"><Button>Back to Dashboard</Button></Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <main className="container mx-auto px-4 py-8 max-w-6xl">
        <ErrorDisplay error={error} />

        {!isFeaturedMode && (
          <>
            <DeleteAnalysisModal 
              isOpen={showDeleteConfirm}
              onClose={() => setShowDeleteConfirm(false)}
              onConfirm={handleDeleteAnalysis}
              isDeleting={isDeleting}
            />

            <ShareAnalysisModal 
              isOpen={showShareModal}
              onClose={() => setShowShareModal(false)}
              analysis={analysis}
              shareHandlers={{
                shareToTwitter,
                shareToLinkedIn,
                shareToWhatsApp,
                shareToInstagram,
                copyToClipboard
              }}
            />
          </>
        )}

        {console.log('Rendering video/campaign analysis components. analysis:', analysis, 'videoMetadata:', videoMetadata, 'youtubeMetadata:', youtubeMetadata)}
        {analysis && (analysis.video_url || youtubeVideoId) && (
          <div className="mb-6">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-semibold text-gray-900 truncate overflow-hidden">{youtubeMetadata?.title || videoMetadata.title}</h1>
              </div>
              {!isFeaturedMode && (
                <div className="flex items-center gap-2 shrink-0">
                  {/* Publish/Unpublish Toggle Button */}
                  {isAuthenticated && analysis && (
                    <Button
                      onClick={togglePublicStatus}
                      variant={analysis.is_public ? "outline" : "default"}
                      size="sm"
                      disabled={togglePublicLoading}
                      className={`flex items-center gap-2 ${
                        analysis.is_public 
                          ? "border-red-300 text-red-600 hover:bg-red-50 hover:text-red-700" 
                          : "bg-green-600 hover:bg-green-700 text-white"
                      }`}
                    >
                      {analysis.is_public ? (
                        <>
                          <Globe className="h-4 w-4" />
                          Unpublish
                        </>
                      ) : (
                        <>
                          <Globe className="h-4 w-4" />
                          Publish
                        </>
                      )}
                    </Button>
                  )}
                  {/* Share Button */}
                  <Button
                    onClick={handleShare}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Share2 className="h-4 w-4" />
                    Share
                  </Button>
                </div>
              )}
            </div>
            {/* Badges - Full width below title and buttons */}
            <div className="flex flex-wrap gap-2 mt-4">
              {getBadges().map((badge, index) => (
                <Badge key={index} variant={badge.variant} className="bg-blue-100 text-blue-800 border-blue-200">
                  {badge.label}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Two Column Layout for Video and Campaign Analysis */}
        {analysis && (analysis.video_url || youtubeVideoId) && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8 lg:items-stretch">
            <div className="order-1 lg:order-1 lg:col-span-2 flex">
              <VideoPlayer 
                showVideo={showVideo}
                youtubeVideoId={youtubeVideoId}
                videoMetadata={videoMetadata}
                onShowVideo={() => setShowVideo(true)}
              />
            </div>
            <div className="order-2 lg:order-2 flex">
              <CampaignAnalysis 
                youtubeMetadata={youtubeMetadata}
                videoMetadata={videoMetadata}
              />
            </div>
          </div>
        )}

        {/* Skip step 1 - pending analysis card is disabled */}

        {analysis?.status === 'processing' && (
          <ProcessingStatusCard />
        )}

        {(analysis?.status === 'completed' || analysis?.status === 'generated' || analysis?.status === 'pending') && (
          <MarketingAnalysisView 
            marketingAnalysis={marketingAnalysis}
            generateFeature={generateFeature}
            marketingCopy={marketingCopy}
            socialMediaPosts={socialMediaPosts}
            marketingScorecard={marketingScorecard}
            seoKeywords={seoKeywords}
            contentSuggestions={contentSuggestions}
            copyLoading={copyLoading}
            socialLoading={socialLoading}
            scorecardLoading={scorecardLoading}
            keywordsLoading={keywordsLoading}
            suggestionsLoading={suggestionsLoading}
            loading={loading}
            setMarketingCopy={setMarketingCopy}
            setSocialMediaPosts={setSocialMediaPosts}
            setMarketingScorecard={setMarketingScorecard}
            setSeoKeywords={setSeoKeywords}
            setContentSuggestions={setContentSuggestions}
            setCopyLoading={setCopyLoading}
            setSocialLoading={setSocialLoading}
            setScorecardLoading={setScorecardLoading}
            setKeywordsLoading={setKeywordsLoading}
            setSuggestionsLoading={setSuggestionsLoading}
            isFeaturedMode={isFeaturedMode}
          />
        )}
      </main>
      
      {/* Login Overlay for unauthenticated users viewing public analyses */}
      {showLoginOverlay && !isAuthenticated && analysis?.is_public && !isFeaturedMode && (
        <LoginOverlay 
          isVisible={showLoginOverlay}
          title="Unlock Full Analysis"
          description="Sign up to access the complete analysis with detailed insights, AI-powered recommendations, and advanced features."
        />
      )}
    </div>
  )
}

// Marketing Analysis View Component (imported from original ad/[id] page)
const MarketingAnalysisView = ({ 
  marketingAnalysis, 
  generateFeature,
  marketingCopy,
  socialMediaPosts,
  marketingScorecard,
  seoKeywords,
  contentSuggestions,
  copyLoading,
  socialLoading,
  scorecardLoading,
  keywordsLoading,
  suggestionsLoading,
  loading,
  setMarketingCopy,
  setSocialMediaPosts,
  setMarketingScorecard,
  setSeoKeywords,
  setContentSuggestions,
  setCopyLoading,
  setSocialLoading,
  setScorecardLoading,
  setKeywordsLoading,
  setSuggestionsLoading,
  isFeaturedMode = false
}: {
  marketingAnalysis: any
  generateFeature: any
  marketingCopy: string
  socialMediaPosts: string
  marketingScorecard: string
  seoKeywords: string
  contentSuggestions: string
  copyLoading: boolean
  socialLoading: boolean
  scorecardLoading: boolean
  keywordsLoading: boolean
  suggestionsLoading: boolean
  loading: boolean
  setMarketingCopy: any
  setSocialMediaPosts: any
  setMarketingScorecard: any
  setSeoKeywords: any
  setContentSuggestions: any
  setCopyLoading: any
  setSocialLoading: any
  setScorecardLoading: any
  setKeywordsLoading: any
  setSuggestionsLoading: any
  isFeaturedMode?: boolean
}) => {
  
  // Parse marketing analysis JSON
  let analysisData = null;
  try {
    if (typeof marketingAnalysis === 'string') {
      // Clean up the string by removing markdown code blocks and extra whitespace
      let cleanedString = marketingAnalysis.trim();
      
      // Remove markdown code blocks if present
      if (cleanedString.startsWith('```json')) {
        cleanedString = cleanedString.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanedString.startsWith('```')) {
        cleanedString = cleanedString.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Remove any remaining backticks at the start or end
      cleanedString = cleanedString.replace(/^`+|`+$/g, '').trim();
      
      // Try to parse the cleaned string
      if (cleanedString) {
        analysisData = JSON.parse(cleanedString);
      }
    } else if (typeof marketingAnalysis === 'object') {
      analysisData = marketingAnalysis;
    }
  } catch (error) {
    console.error('Failed to parse marketing analysis:', error);
    console.error('Raw marketing analysis:', marketingAnalysis);
  }

  // If no marketing analysis data, show generation button or raw content
  if (!analysisData) {
    // If there's raw marketing analysis content but parsing failed, show it
    if (marketingAnalysis && typeof marketingAnalysis === 'string' && marketingAnalysis.trim()) {
      return (
        <div className="max-w-7xl mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle>Marketing Analysis (Raw Format)</CardTitle>
              <CardDescription>
                The analysis is available but couldn&apos;t be parsed into the interactive format. Raw content is shown below.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded-lg overflow-x-auto">
                  {marketingAnalysis}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }
    
    // If no marketing analysis at all, show generation button (only for non-featured mode)
    if (!isFeaturedMode) {
      return (
        <div className="max-w-7xl mx-auto px-4 py-8">
          <Card className="text-center">
            <CardHeader>
              <CardTitle>Marketing Analysis Not Generated</CardTitle>
              <CardDescription>
                Generate a comprehensive marketing analysis to see detailed insights about this ad.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={() => window.location.reload()}
                disabled={loading}
              >
                {loading ? 'Generating...' : 'Generate Marketing Analysis'}
              </Button>
            </CardContent>
          </Card>
        </div>
      );
    } else {
      // For featured mode, show a message that analysis is not available
      return (
        <div className="max-w-7xl mx-auto px-4 py-8">
          <Card className="text-center">
            <CardHeader>
              <CardTitle>Analysis Temporarily Unavailable</CardTitle>
              <CardDescription>
                This featured analysis is currently being processed. Please check back soon.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      );
    }
  }

  // Helper components removed for simplicity

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="space-y-8">

        {/* Top Row - Full Width */}
        {/* Overall Impression Testimonial */}
        {analysisData.gut_reaction && (
          <OverallImpressionTestimonial impression={analysisData.gut_reaction} />
        )}

          <PerformanceScorecard scorecard={analysisData.scorecard} />

        {/* 3rd Row - What Worked & What Didn't */}
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 items-start">

        {/* What Worked Like Gangbusters */}
          {analysisData.veteran_verdict?.what_worked_gangbusters && (
            <div className="lg:col-span-2">
              <AnalysisInsightCard
                title="What Works Well"
                content={Array.isArray(analysisData.veteran_verdict.what_worked_gangbusters) 
                  ? analysisData.veteran_verdict.what_worked_gangbusters.join('. ') 
                  : analysisData.veteran_verdict.what_worked_gangbusters}
                icon={Trophy}
                variant="success"
              />
            </div>
          )}

          {/* What Missed the Mark */}
          {analysisData.veteran_verdict?.what_missed_mark && (
            <div className="lg:col-span-2">
              <AnalysisInsightCard
                title="What Missed the Mark"
                content={Array.isArray(analysisData.veteran_verdict.what_missed_mark) 
                  ? analysisData.veteran_verdict.what_missed_mark.join('. ') 
                  : analysisData.veteran_verdict.what_missed_mark}
                icon={AlertTriangle}
                variant="warning"
              />
            </div>
          )}

        </div>
        
        {/* 4th Row - Framework Analysis and Central Insight */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 items-start">
        {/* Framework Analysis */}
          {analysisData.system_signals?.framework_analysis && (
            <div className="lg:col-span-2">
              <AnalysisInsightCard
                title="Primary Framework"
                content={analysisData.system_signals.framework_analysis.primary_framework}
                icon={Brain}
                variant="primary"
              />
            </div>
          )}

          {/* Central Insight */}
            {analysisData.lightning_round?.central_insight && (
              <div className="lg:col-span-2">
                <AnalysisInsightCard
                  title="The Central Insight"
                  content={analysisData.lightning_round.central_insight}
                  icon={Quote}
                  variant="insight"
                />
              </div>
            )}

        </div>

        {/* Professional Breakdown Section */}
        {analysisData.professional_breakdown && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Professional Breakdown</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
   
              {/* Business Integration - 2x2 Grid */}
              {analysisData.professional_breakdown.business_integration && (
                <div className="bg-white border border-gray-200 rounded-lg p-6 md:col-span-2">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2 text-green-600" />
                    Business Integration
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(analysisData.professional_breakdown.business_integration).map(([key, value]) => (
                      <div key={key} className="bg-green-50 p-4 rounded-lg border border-green-200">
                        <h4 className="font-semibold text-green-900 mb-2 text-sm capitalize">{key.replace(/_/g, ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Strategic Deep Dive Section */}
        {analysisData.strategic_deep_dive && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Strategic Deep Dive</h2>
            <div className="space-y-6">
              {/* Target Audience Read - Full Width */}
              {analysisData.strategic_deep_dive.target_audience_read && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Users className="w-5 h-5 mr-2 text-blue-600" />
                    Target Audience Read
                  </h3>
                  {typeof analysisData.strategic_deep_dive.target_audience_read === 'string' ? (
                    <p className="text-gray-700 text-sm leading-relaxed">{analysisData.strategic_deep_dive.target_audience_read}</p>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(analysisData.strategic_deep_dive.target_audience_read).map(([key, value]) => (
                        <div key={key} className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <h4 className="font-semibold text-blue-900 mb-2 text-sm capitalize">{key.replace(/_/g, ' ')}:</h4>
                          <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Competitive Context and Cultural Relevance - Two Columns */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Competitive Context */}
                {analysisData.strategic_deep_dive.competitive_context && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                      <Trophy className="w-5 h-5 mr-2 text-purple-600" />
                      Competitive Context
                    </h3>
                    <div className="space-y-3">
                      {Object.entries(analysisData.strategic_deep_dive.competitive_context).map(([key, value]) => (
                        <div key={key} className="bg-purple-50 p-3 rounded-lg border border-purple-200">
                          <h4 className="font-semibold text-purple-900 mb-2 text-sm capitalize">{key.replace(/_/g, ' ')}:</h4>
                          <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Cultural Relevance */}
                {analysisData.strategic_deep_dive.cultural_relevance && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                      <Globe className="w-5 h-5 mr-2 text-green-600" />
                      Cultural Relevance
                    </h3>
                    <div className="space-y-3">
                      {Object.entries(analysisData.strategic_deep_dive.cultural_relevance).map(([key, value]) => (
                        <div key={key} className="bg-green-50 p-3 rounded-lg border border-green-200">
                          <h4 className="font-semibold text-green-900 mb-2 text-sm capitalize">{key.replace(/_/g, ' ')}:</h4>
                          <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

          {/* Pattern Recognition & Predictions Section */}
          {(analysisData.system_signals?.pattern_recognition || analysisData.system_signals?.prediction_factors) && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Advanced Analysis</h2>
              <div className="space-y-6">
                {/* Pattern Recognition - 2x2 Grid */}
                {analysisData.system_signals?.pattern_recognition && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                      <HashIcon className="w-5 h-5 mr-2 text-orange-600" />
                      Pattern Recognition
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(analysisData.system_signals.pattern_recognition).map(([key, value]) => (
                        <div key={key} className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                          <h4 className="font-semibold text-orange-900 mb-2 text-sm capitalize">{key.replace(/_/g, ' ')}:</h4>
                          <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Performance Predictions - 2x2 Grid */}
                {analysisData.system_signals?.prediction_factors && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                      <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
                      Performance Predictions
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(analysisData.system_signals.prediction_factors).map(([key, value]) => (
                        <div key={key} className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                          <h4 className="font-semibold text-blue-900 mb-2 text-sm capitalize">{key.replace(/_/g, ' ')}:</h4>
                          <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

        {/* Pitfalls Identified */}
        {analysisData.pitfalls_identified && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Pitfalls Identified</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {analysisData.pitfalls_identified.strategic_pitfalls && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-red-900 mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2 text-red-600" />
                    Strategic Pitfalls
                  </h3>
                  <p className="text-red-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.strategic_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.audience_pitfalls && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-orange-900 mb-4 flex items-center">
                    <Users className="w-5 h-5 mr-2 text-orange-600" />
                    Audience Pitfalls
                  </h3>
                  <p className="text-orange-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.audience_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.creative_pitfalls && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-yellow-900 mb-4 flex items-center">
                    <Lightbulb className="w-5 h-5 mr-2 text-yellow-600" />
                    Creative Pitfalls
                  </h3>
                  <p className="text-yellow-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.creative_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.messaging_pitfalls && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-purple-900 mb-4 flex items-center">
                    <MessageSquare className="w-5 h-5 mr-2 text-purple-600" />
                    Messaging Pitfalls
                  </h3>
                  <p className="text-purple-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.messaging_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.ethical_cultural_pitfalls && (
                <div className="bg-pink-50 border border-pink-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-pink-900 mb-4 flex items-center">
                    <AlertTriangle className="w-5 h-5 mr-2 text-pink-600" />
                    Ethical & Cultural Pitfalls
                  </h3>
                  <p className="text-pink-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.ethical_cultural_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.media_placement_pitfalls && (
                <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-indigo-900 mb-4 flex items-center">
                    <Monitor className="w-5 h-5 mr-2 text-indigo-600" />
                    Media & Placement Pitfalls
                  </h3>
                  <p className="text-indigo-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.media_placement_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.competitive_market_pitfalls && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Trophy className="w-5 h-5 mr-2 text-gray-600" />
                    Competitive & Market Pitfalls
                  </h3>
                  <p className="text-gray-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.competitive_market_pitfalls}</p>
                </div>
              )}
            </div>
          </div>
        )}

          {/* Improvement Signals */}
          {analysisData.system_signals?.improvement_signal_specifics?.issues?.length > 0 && (
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <Settings className="w-6 h-6 mr-2 text-orange-600" />
                Improvement Signals Detected
              </h2>
              {analysisData.system_signals.improvement_signal_specifics.issues.map((signal: any, index: number) => (
                <div key={index} className="bg-orange-50 p-5 rounded-lg border border-orange-200 mb-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-orange-900">{signal.element}</h3>
                    <span className="text-sm font-medium text-orange-700 bg-orange-200 px-2 py-1 rounded">Needs Improvement</span>
                  </div>
                  <div className="space-y-3 text-sm">
                    <div>
                      <span className="font-medium text-orange-900">Specific Issue:</span>
                      <p className="text-orange-800 mt-1">{signal.specific_issue}</p>
                    </div>
                    <div>
                      <span className="font-medium text-orange-900">Root Cause:</span>
                      <p className="text-orange-800 mt-1">{signal.root_cause}</p>
                    </div>
                    <div>
                      <span className="font-medium text-orange-900">Improvement Direction:</span>
                      <p className="text-orange-800 mt-1">{signal.improvement_direction}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* The Killer Insight */}
          {analysisData.veteran_verdict?.killer_insight && (
            <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border-l-4 border-indigo-500">
              <h2 className="text-xl font-bold text-indigo-900 mb-4 flex items-center">
                <Brain className="w-6 h-6 mr-2" />
                The Killer Insight
              </h2>
              <p className="text-indigo-800 font-medium leading-relaxed text-lg">
                {analysisData.veteran_verdict.killer_insight}
              </p>
            </div>
          )}

          {/* If This Was Your Campaign */}
          {analysisData.veteran_verdict?.if_this_was_your_campaign && (
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <Lightbulb className="w-6 h-6 mr-2 text-yellow-600" />
                If This Was Your Campaign
              </h2>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-blue-800 text-sm">{analysisData.veteran_verdict.if_this_was_your_campaign}</p>
              </div>
            </div>
          )}

          {/* The Big Learning */}
          {analysisData.veteran_verdict?.big_learning && (
            <div className="bg-gradient-to-r from-gray-900 to-gray-800 text-white rounded-xl p-6">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <Award className="w-6 h-6 mr-2" />
                The Big Learning
              </h2>
              <p className="font-medium leading-relaxed text-lg">
                {analysisData.veteran_verdict.big_learning}
              </p>
            </div>
          )}
        </div>

        {/* Action Buttons for additional LLM features - only show for non-featured mode */}
      {!isFeaturedMode && (
        <div className="mt-8 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Generate More Insights</CardTitle>
              <CardDescription>
                Use your credits to generate additional marketing assets and analysis based on this video.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Button
                  onClick={() => generateFeature('marketing_copy', setMarketingCopy, setCopyLoading)}
                  disabled={copyLoading || loading || !!marketingCopy}
                  className="w-full"
                >
                  {marketingCopy ? 'Copy Generated' : (copyLoading ? 'Generating...' : 'Generate Marketing Copy (1 Credit)')}
                </Button>
                <Button
                  onClick={() => generateFeature('social_media_posts', setSocialMediaPosts, setSocialLoading)}
                  disabled={socialLoading || loading || !!socialMediaPosts}
                  className="w-full"
                >
                  {socialMediaPosts ? 'Posts Generated' : (socialLoading ? 'Generating...' : 'Propose Social Posts (1 Credit)')}
                </Button>
                <Button
                  onClick={() => generateFeature('marketing_scorecard', setMarketingScorecard, setScorecardLoading)}
                  disabled={scorecardLoading || loading || !!marketingScorecard}
                  className="w-full"
                >
                  {marketingScorecard ? 'Scorecard Generated' : (scorecardLoading ? 'Generating...' : 'Generate Scorecard (1 Credit)')}
                </Button>
                <Button
                  onClick={() => generateFeature('seo_keywords', setSeoKeywords, setKeywordsLoading)}
                  disabled={keywordsLoading || loading || !!seoKeywords}
                  className="w-full"
                >
                  {seoKeywords ? 'Keywords Generated' : (keywordsLoading ? 'Generating...' : 'Extract SEO Keywords (1 Credit)')}
                </Button>
                <Button
                  onClick={() => generateFeature('content_suggestions', setContentSuggestions, setSuggestionsLoading)}
                  disabled={suggestionsLoading || loading || !!contentSuggestions}
                  className="w-full"
                >
                  {contentSuggestions ? 'Suggestions Generated' : (suggestionsLoading ? 'Generating...' : 'Suggest Improvements (1 Credit)')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {!isFeaturedMode && (
        <GeneratedContentSections
          marketingCopy={marketingCopy}
          socialMediaPosts={socialMediaPosts}
          marketingScorecard={marketingScorecard}
          seoKeywords={seoKeywords}
          contentSuggestions={contentSuggestions}
        />
      )}
    </div>
  );
};
'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Globe, Share2, Lightbulb, AlertTriangle,
  MessageSquare, BarChart3
} from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import ProcessingStatusCard from '@/components/analysis/ProcessingStatusCard'
import VideoPlayer from '@/components/analysis/VideoPlayer'
import LightningRound from '@/components/analysis/LightningRound'
import { getMarketingAnalysisPrompt } from '@/lib/prompts/marketingAnalysisPrompt'
import { getMarketingCopyPrompt } from '@/lib/prompts/marketingCopyPrompt'
import { getSocialMediaPostsPrompt } from '@/lib/prompts/socialMediaPostsPrompt'
import { getMarketingScorecardPrompt } from '@/lib/prompts/marketingScorecardPrompt'
import { getSeoKeywordsPrompt } from '@/lib/prompts/seoKeywordsPrompt'
import { getContentSuggestionsPrompt } from '@/lib/prompts/contentSuggestionsPrompt'
import LoginOverlay from '@/components/ui/LoginOverlay'
import Navigation from '@/components/Navigation'
import DeleteAnalysisModal from '@/components/analysis/DeleteAnalysisModal'
import ShareAnalysisModal from '@/components/analysis/ShareAnalysisModal'
import LoadingSkeleton from '@/components/analysis/LoadingSkeleton'
import ErrorDisplay from '@/components/analysis/ErrorDisplay'
import GeneratedContentSections from '@/components/analysis/GeneratedContentSections'
import TabsComponent from '@/components/analysis/TabsComponent'
import ExecutiveBriefingCard from '@/components/analysis/ExecutiveBriefingCard'
import CoreAnalysisCard from '@/components/analysis/CoreAnalysisCard'
import ScorecardSidebar from '@/components/analysis/ScorecardSidebar'
import MetadataSidebar from '@/components/analysis/MetadataSidebar'
import DiagnosisCard from '@/components/analysis/DiagnosisCard'

// Interfaces
interface VideoMetadata {
  title: string
  thumbnail: string
  duration: string
  inferredBrandName: string
}

interface YouTubeMetadata {
  title: string
  description: string
  channelTitle: string
  publishedAt: string
  viewCount: string
  likeCount: string
  commentCount: string
  tags: string[]
  categoryId: string
  defaultLanguage?: string
  duration: string
  definition: string
}

interface SharedAnalysisPageProps {
  analysisId: string
  isFeaturedMode?: boolean
  preloadedAnalysis?: any
}

// Main Component
export default function SharedAnalysisPage({ 
  analysisId, 
  isFeaturedMode = false, 
  preloadedAnalysis = null 
}: SharedAnalysisPageProps) {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const { checkCredits, deductCredits, loading: creditsLoading } = useCredits()
  const [analysis, setAnalysis] = useState<any>(preloadedAnalysis)
  const [loading, setLoading] = useState(!preloadedAnalysis)
  const [error, setError] = useState('')
  const [showVideo, setShowVideo] = useState(false)
  const [youtubeVideoId, setYoutubeVideoId] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [youtubeMetadata, setYoutubeMetadata] = useState<YouTubeMetadata | null>(null)
  const [showLoginOverlay, setShowLoginOverlay] = useState(false)
  const [isInitialLoading, setIsInitialLoading] = useState(!preloadedAnalysis)

  // Core analysis data states (from prototype)
  const [videoInfo, setVideoInfo] = useState('') // Raw transcript + summary text
  const [marketingAnalysis, setMarketingAnalysis] = useState('') // Detailed 10-point analysis
  const [parsedData, setParsedData] = useState<any>(null) // Centralized parsed marketing analysis

  // Generated content states
  const [marketingCopy, setMarketingCopy] = useState('')
  const [socialMediaPosts, setSocialMediaPosts] = useState('')
  const [marketingScorecard, setMarketingScorecard] = useState('')
  const [seoKeywords, setSeoKeywords] = useState('')
  const [contentSuggestions, setContentSuggestions] = useState('')
  
  // Loading states for individual features
  const [copyLoading, setCopyLoading] = useState(false)
  const [socialLoading, setSocialLoading] = useState(false)
  const [scorecardLoading, setScorecardLoading] = useState(false)
  const [keywordsLoading, setKeywordsLoading] = useState(false)
  const [suggestionsLoading, setSuggestionsLoading] = useState(false)
  const [togglePublicLoading, setTogglePublicLoading] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)

  // Centralized data parsing function
  const parseMarketingAnalysis = useCallback((data: any) => {
    if (!data) return null
    try {
      if (typeof data === 'string') {
        let cleanData = data
          .replace(/```json\s*/g, '')
          .replace(/```\s*/g, '')
          .replace(/^\s*```.*$/gm, '')
          .trim()
        
        const jsonMatch = cleanData.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          cleanData = jsonMatch[0]
        }
        
        const parsed = JSON.parse(cleanData)
        return parsed
      }
      return data
    } catch (error) {
      console.error('Failed to parse marketing analysis:', error)
      return null
    }
  }, [])

  // Update parsed data whenever marketing analysis changes
  useEffect(() => {
    const newParsedData = parseMarketingAnalysis(marketingAnalysis)
    setParsedData(newParsedData)
  }, [marketingAnalysis, parseMarketingAnalysis])

  // Extract badges data from analysis
  const getBadges = () => {
    const badges = []
    
    // Brand name - prefer from parsed metadata, fallback to current analysis
    const brandName = parsedData?.metadata?.brand || analysis?.inferred_brand
    if (brandName) {
      badges.push({ label: brandName, variant: 'secondary' as const })
    }
    
    // Product category - prefer from parsed metadata, fallback to current analysis
    const productCategory = parsedData?.metadata?.product_category || analysis?.product_category
    if (productCategory) {
      badges.push({ label: productCategory, variant: 'secondary' as const })
    }
    
    // Campaign category - prefer from parsed metadata, fallback to current analysis
    const campaignCategory = parsedData?.metadata?.campaign_category || analysis?.campaign_category
    if (campaignCategory) {
      badges.push({ label: campaignCategory, variant: 'secondary' as const })
    }
    
    // Celebrity - from parsed metadata
    const celebrity = parsedData?.metadata?.celebrity
    if (celebrity && celebrity.trim() !== '' && celebrity.toLowerCase() !== 'none' && celebrity.toLowerCase() !== 'n/a') {
      // Handle multiple celebrities separated by commas or "and"
      const celebrityList = celebrity.split(/,|\band\b/i).map((name: string) => name.trim()).filter((name: string) => name.length > 0)
      
      // Only add first celebrity to keep badges manageable
      if (celebrityList.length > 0) {
        badges.push({ label: celebrityList[0], variant: 'secondary' as const })
      }
    }
    
    return badges
  }

  // Data states
  const [videoMetadata, setVideoMetadata] = useState<VideoMetadata>({
    title: 'Loading analysis...',
    thumbnail: 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Loading...',
    duration: '0:00',
    inferredBrandName: 'Loading...'
  })
  
  // Removed unused detailedAnalysisData state

  const formatDuration = (seconds: number | null) => {
    if (seconds === null || seconds === undefined) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.round(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'hqdefault' | 'mqdefault' | 'sddefault' | 'maxresdefault' = 'maxresdefault'): string => {
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`
  }

  const fetchYouTubeMetadata = async (videoId: string) => {
    try {
      const API_KEY = process.env.NEXT_PUBLIC_YOUTUBE_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY
      if (!API_KEY) {
        console.warn('No YouTube API key available')
        return
      }

      const response = await fetch(
        `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${API_KEY}&part=snippet,statistics,contentDetails`
      )
      
      if (!response.ok) {
        throw new Error('Failed to fetch YouTube metadata')
      }
      
      const data = await response.json()
      
      if (data.items && data.items.length > 0) {
        const video = data.items[0]
        const snippet = video.snippet
        const statistics = video.statistics
        const contentDetails = video.contentDetails
        
        // Parse ISO 8601 duration (PT4M13S -> 4:13)
        const parseDuration = (duration: string) => {
          const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
          if (!match) return '0:00'
          
          const hours = parseInt(match[1] || '0')
          const minutes = parseInt(match[2] || '0')
          const seconds = parseInt(match[3] || '0')
          
          if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
          }
          return `${minutes}:${seconds.toString().padStart(2, '0')}`
        }
        
        const metadata: YouTubeMetadata = {
          title: snippet.title || 'Untitled Video',
          description: snippet.description || 'No description available',
          channelTitle: snippet.channelTitle || 'Unknown Channel',
          publishedAt: snippet.publishedAt,
          viewCount: statistics.viewCount || '0',
          likeCount: statistics.likeCount || '0',
          commentCount: statistics.commentCount || '0',
          tags: snippet.tags || [],
          categoryId: snippet.categoryId || '',
          defaultLanguage: snippet.defaultLanguage,
          duration: parseDuration(contentDetails.duration),
          definition: contentDetails.definition || 'sd'
        }
        
        setYoutubeMetadata(metadata)
      }
    } catch (error) {
      console.error('Error fetching YouTube metadata:', error)
    }
  }

  const parseAndSetData = useCallback((analysisData: any) => {
    console.log('parseAndSetData: Received analysis data', analysisData)
    setAnalysis(analysisData)
    
    // Extract YouTube video ID from URL first
    let videoId: string | null = null
    if (analysisData.video_url) {
      videoId = extractYouTubeVideoId(analysisData.video_url)
      setYoutubeVideoId(videoId)
      
      // Fetch YouTube metadata
      if (videoId) {
        fetchYouTubeMetadata(videoId)
      }
    }
    
    // Also check if we have a direct youtube_video_id field
    if (analysisData.youtube_video_id) {
      setYoutubeVideoId(analysisData.youtube_video_id)
      
      // Fetch YouTube metadata if we don't already have it
      if (!videoId) {
        fetchYouTubeMetadata(analysisData.youtube_video_id)
      }
    }
    
    // Use YouTube thumbnail if we have a video ID, otherwise fallback to stored thumbnail
    const thumbnailUrl = videoId 
      ? getYouTubeThumbnail(videoId, 'maxresdefault')
      : analysisData.video_thumbnail_url || analysisData.thumbnail_url || 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail'
    
    setVideoMetadata({
      title: analysisData.video_title || analysisData.title || 'No Title',
      thumbnail: thumbnailUrl,
      duration: formatDuration(analysisData.video_duration || analysisData.duration_seconds),
      inferredBrandName: analysisData.inferred_brand || 'N/A'
    })

    // Set video info and marketing analysis from database (matching prototype structure)
    if (analysisData.video_info) {
      setVideoInfo(analysisData.video_info)
    } else if (analysisData.transcript && analysisData.summary) {
      setVideoInfo(analysisData.transcript + "\n\n**Summary:**\n" + analysisData.summary)
    }
    
    if (analysisData.marketing_analysis) {
      setMarketingAnalysis(analysisData.marketing_analysis)
    }

    // Set generated content from database
    if (analysisData.marketing_copy) setMarketingCopy(analysisData.marketing_copy)
    if (analysisData.social_media_posts) setSocialMediaPosts(analysisData.social_media_posts)
    if (analysisData.marketing_scorecard) setMarketingScorecard(analysisData.marketing_scorecard)
    if (analysisData.seo_keywords) setSeoKeywords(analysisData.seo_keywords)
    if (analysisData.content_suggestions) setContentSuggestions(analysisData.content_suggestions)
    // Enhanced script handling removed for simplicity

    // Simplified data handling - detailed analysis data removed for cleaner code

    // Populate other generated reports
    analysisData.reports?.forEach((report: any) => {
      if (report.status === 'generated') {
        const content = formatGeneratedContent(report.report_types.name, report.content)
        switch (report.report_types.name) {
          case 'marketing_copy': setMarketingCopy(content); break;
          case 'social_media_posts': setSocialMediaPosts(content); break;
          case 'marketing_scorecard': setMarketingScorecard(content); break;
          case 'seo_keywords': setSeoKeywords(content); break;
          case 'content_suggestions': setContentSuggestions(content); break;
          // Removed emotion_timeline and enhanced_script cases for simplicity
        }
      }
    })
  }, [])

  const fetchBasicAnalysis = useCallback(async () => {
    if (!analysisId) return
    setIsInitialLoading(true)
    setLoading(true)
    try {
      // First, fetch basic data quickly for above-the-fold content with cache-busting
      const timestamp = Date.now()
      const basicRes = await fetch(`/api/analyses/${analysisId}/basic?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!basicRes.ok) {
        const errData = await basicRes.json()
        throw new Error(errData.error || 'Failed to fetch analysis data.')
      }
      const basicData = await basicRes.json()
      console.log('fetchBasicAnalysis: Basic data fetched (cache-busted)', basicData)
      
      // Set basic data immediately for fast rendering
      setAnalysis(basicData)
      setIsInitialLoading(false)
      setLoading(false)
      
      // Then fetch full data in background with a small delay to improve perceived performance
      setTimeout(async () => {
        try {
          const fullTimestamp = Date.now()
          const fullRes = await fetch(`/api/analyses/${analysisId}?_t=${fullTimestamp}`, {
            cache: 'no-store',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          })
          if (fullRes.ok) {
            const fullData = await fullRes.json()
            parseAndSetData(fullData)
          }
        } catch (err) {
          console.warn('Failed to load full analysis data:', err)
        }
      }, 100)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setIsInitialLoading(false)
      setLoading(false)
    }
  }, [analysisId, parseAndSetData])

  const fetchAnalysis = useCallback(async () => {
    if (!analysisId) return
    setLoading(true)
    try {
      const timestamp = Date.now()
      const res = await fetch(`/api/analyses/${analysisId}?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!res.ok) {
        const errData = await res.json()
        throw new Error(errData.error || 'Failed to fetch analysis data.')
      }
      const data = await res.json()
      
      console.log('fetchAnalysis: Fresh data fetched (cache-busted)', data)
      
      parseAndSetData(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
    } finally {
      setLoading(false)
    }
  }, [analysisId, parseAndSetData])

  useEffect(() => {
    // Skip data fetching if we have preloaded analysis
    if (preloadedAnalysis) {
      parseAndSetData(preloadedAnalysis)
      return
    }

    // Use progressive loading when authenticated OR try loading public analysis
    if ((isAuthenticated && !authLoading) || (!authLoading && !isAuthenticated)) {
      fetchBasicAnalysis()
    }
  }, [isAuthenticated, authLoading, fetchBasicAnalysis, preloadedAnalysis, parseAndSetData])

  // Scroll detection for login overlay (only for non-featured mode)
  useEffect(() => {
    // Skip scroll listener for featured mode or authenticated users
    if (isFeaturedMode || isAuthenticated || !analysis?.is_public) {
      return
    }

    const handleScroll = () => {
      const scrollPosition = window.scrollY
      const windowHeight = window.innerHeight
      
      // Show overlay when user scrolls past the second "fold" 
      // This is approximately after the video player and campaign analysis sections
      // Use a more conservative trigger point - 2 viewport heights or when user scrolls significantly
      const triggerPoint = Math.max(windowHeight * 1.2, 800) // Either 1.2 viewport heights or 800px minimum
      
      if (scrollPosition >= triggerPoint && !showLoginOverlay) {
        setShowLoginOverlay(true)
      }
    }

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true })
    
    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [isFeaturedMode, isAuthenticated, analysis?.is_public, showLoginOverlay])

  // Tab handling removed for simplified component

  const handleDeleteAnalysis = async () => {
    setIsDeleting(true)
    setError('')
    
    try {
      const response = await fetch(`/api/analyses/${analysisId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete analysis')
      }
      
      // Redirect to dashboard after successful deletion
      window.location.href = '/dashboard'
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete analysis')
      setIsDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  const callGeminiApi = async (_prompt: string, modelName: string, parts: any[], isJsonResponse = false) => {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || "AIzaSyBoI6_LcEKmsmLVJXE7Q_Fe72WmwlqOL9M"
    
    // Define fallback models in order of preference
    const fallbackModels = [
      modelName, // Original requested model
      'gemini-1.5-pro', // First fallback
      'gemini-1.5-flash' // Second fallback
    ]
    
    let chatHistory = []
    chatHistory.push({ role: "user", parts: parts })

    const payload: any = { contents: chatHistory }
    if (isJsonResponse) {
      payload.generationConfig = {
        responseMimeType: "application/json",
      }
    }

    // Try each model in sequence if previous ones fail
    for (let i = 0; i < fallbackModels.length; i++) {
      const currentModel = fallbackModels[i]
      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${currentModel}:generateContent?key=${apiKey}`

      try {
        console.log(`🤖 Trying model: ${currentModel}`)
        console.log(`🔗 API URL: ${apiUrl}`)
        console.log(`📦 Payload size: ${JSON.stringify(payload).length} bytes`)
        
        // Add timeout and additional headers for better reliability
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 240000) // 60 second timeout (increased from 30)
        
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(payload),
          signal: controller.signal
        })

        clearTimeout(timeoutId)

        console.log(`📡 Response status: ${response.status} ${response.statusText}`)

        if (!response.ok) {
          let errorData;
          try {
            errorData = await response.json()
          } catch (jsonError) {
            console.error('Failed to parse error response as JSON:', jsonError)
            errorData = { error: { message: `HTTP ${response.status}: ${response.statusText}` } }
          }
          
          // If it's a 503 Service Unavailable, try the next model
          if (response.status === 503 && i < fallbackModels.length - 1) {
            console.warn(`⚠️ Model ${currentModel} unavailable (503), trying fallback...`)
            continue
          }
          
          throw new Error(`API Error (${response.status}): ${errorData.error?.message || response.statusText}`)
        }

        const result = await response.json()
        console.log(`📄 Response structure:`, Object.keys(result))
        
        if (result.candidates && result.candidates.length > 0 &&
            result.candidates[0].content && result.candidates[0].content.parts &&
            result.candidates[0].content.parts.length > 0) {
          const textContent = result.candidates[0].content.parts[0].text
          console.log(`✅ Successfully used model: ${currentModel}`)
          console.log(`📝 Response length: ${textContent.length} characters`)
          return isJsonResponse ? JSON.parse(textContent) : textContent
        } else {
          console.error('❌ Invalid response structure:', result)
          throw new Error("Could not get valid response from Gemini.")
        }
      } catch (error) {
        // Enhanced error logging for better debugging
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            console.error(`⏱️ Request timeout for model ${currentModel} (60 seconds)`)
          } else if (error.message.includes('Failed to fetch')) {
            console.error(`🌐 Network error for model ${currentModel}:`, error)
            console.error('💡 Possible causes: CORS, network connectivity, or API endpoint issues')
          } else {
            console.error(`❌ Error with model ${currentModel}:`, error)
          }
        } else {
          console.error(`❌ Unknown error with model ${currentModel}:`, error)
        }
        
        // If this is the last model, re-throw the error with more context
        if (i === fallbackModels.length - 1) {
          if (error instanceof Error) {
            if (error.message.includes('Failed to fetch')) {
              throw new Error(`Network error: Unable to connect to Gemini API. Please check your internet connection and try again.`)
            }
            if (error.name === 'AbortError') {
              throw new Error(`Request timeout: The AI analysis is taking longer than expected. Please try again.`)
            }
            throw error
          } else {
            throw new Error(`Unknown error occurred: ${String(error)}`)
          }
        }
        
        // Otherwise, continue to next model
        console.log(`🔄 Trying next fallback model...`)
      }
    }
  }

  const handleTriggerInitialAnalysis = useCallback(async () => {
    setLoading(true)
    setError('')
    
    // Check authentication and credits first
    if (!isAuthenticated) {
      setError('Please sign in to run analysis.')
      setLoading(false)
      return
    }
    
    if (!checkCredits(1)) {
      setError('Insufficient credits. Please upgrade your plan to continue.')
      setLoading(false)
      return
    }
    
    try {
      // Deduct credit first
      const creditDeducted = await deductCredits(1)
      if (!creditDeducted) {
        setError('Failed to process credit. Please try again.')
        setLoading(false)
        return
      }
      
      // First get the analysis record with cache-busting
      const timestamp = Date.now()
      const analysisRes = await fetch(`/api/analyses/${analysisId}?_t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })
      if (!analysisRes.ok) {
        throw new Error('Failed to fetch analysis data.')
      }
      const analysisData = await analysisRes.json()
      
      if (!analysisData.video_url) {
        throw new Error('No video URL found for analysis.')
      }

      // Skip Step 1 - Go directly to Step 2: Generate Marketing Analysis
      const marketingAnalysisPrompt = getMarketingAnalysisPrompt(analysisData.video_url)
      
      const marketingAnalysis = await callGeminiApi(
        marketingAnalysisPrompt,
        'gemini-2.5-pro',
        [
          { text: marketingAnalysisPrompt },
          { fileData: { mimeType: "video/mp4", fileUri: analysisData.video_url } }
        ]
      )

      // Update the analysis with marketing analysis only (skip initial analysis)
      const updateRes = await fetch(`/api/analyses/${analysisId}/update-ai-analysis`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          initialAnalysis: null, // Skip step 1
          marketingAnalysis: marketingAnalysis
        })
      })

      if (!updateRes.ok) {
        throw new Error('Failed to save AI analysis.')
      }

      // Refresh the data
      await fetchAnalysis()

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setLoading(false)
    }
  }, [isAuthenticated, checkCredits, deductCredits, analysisId, fetchAnalysis])

  // Auto-trigger step 2 (marketing analysis) instead of step 1 
  useEffect(() => {
    if (analysis?.status === 'pending' && !authLoading && !loading && !creditsLoading && !isFeaturedMode) {
      if (isAuthenticated && checkCredits(1)) {
        // Add small delay to ensure page is fully loaded
        const timer = setTimeout(() => {
          handleTriggerInitialAnalysis()
        }, 1000)
        return () => clearTimeout(timer)
      }
    }
  }, [analysis?.status, isAuthenticated, authLoading, loading, creditsLoading, checkCredits, handleTriggerInitialAnalysis, isFeaturedMode])

  useEffect(() => {
    if (analysis?.title) {
      document.title = `${analysis.title} - AdBreakdown Analysis`
    } else if (youtubeMetadata?.title) {
      document.title = `${youtubeMetadata.title} - AdBreakdown Analysis`
    } else {
      document.title = 'Loading Analysis - AdBreakdown'
    }
  }, [analysis?.title, youtubeMetadata?.title])

  const generateFeature = async (featureType: string, setContent: Function, setLoadingState: Function) => {
    setLoadingState(true)
    setError('')
    
    // Check authentication and credits first
    if (!isAuthenticated) {
      setError('Please sign in to generate content.')
      setLoadingState(false)
      return
    }
    
    if (!checkCredits(1)) {
      setError('Insufficient credits. Please upgrade your plan to continue.')
      setLoadingState(false)
      return
    }
    
    try {
      // Deduct credit first
      const creditDeducted = await deductCredits(1)
      if (!creditDeducted) {
        setError('Failed to process credit. Please try again.')
        setLoadingState(false)
        return
      }
      
      // Use videoInfo and marketingAnalysis for context (matching prototype)
      if (!videoInfo || !marketingAnalysis) {
        throw new Error('Please complete the initial analysis first to get enough context.')
      }
      let generatedContent = ''
      let prompt = ''

      switch (featureType) {
        case 'marketing_copy':
          prompt = getMarketingCopyPrompt(videoInfo, marketingAnalysis)
          break
        case 'social_media_posts':
          prompt = getSocialMediaPostsPrompt(videoInfo, marketingAnalysis)
          break
        case 'marketing_scorecard':
          prompt = getMarketingScorecardPrompt(marketingAnalysis)
          break
        case 'seo_keywords':
          prompt = getSeoKeywordsPrompt(videoInfo)
          break
        case 'content_suggestions':
          prompt = getContentSuggestionsPrompt(marketingAnalysis)
          break
        default:
          throw new Error(`Unknown feature type: ${featureType}`)
      }

      generatedContent = await callGeminiApi(prompt, 'gemini-1.5-pro', [{ text: prompt }])

      // Save the generated content to database
      const saveRes = await fetch(`/api/analyses/${analysisId}/save-generated-content`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          report_type_name: featureType,
          content: generatedContent
        })
      })

      if (!saveRes.ok) {
        console.warn('Failed to save generated content to database')
      }

      setContent(generatedContent)
      setLoadingState(false)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred.')
      setLoadingState(false)
    }
  }

  // Emotion timeline and enhanced script generation removed for simplified component

  const togglePublicStatus = async () => {
    setTogglePublicLoading(true)
    setError('')

    if (!isAuthenticated) {
      setError('Please sign in to modify analysis visibility.')
      setTogglePublicLoading(false)
      return
    }

    try {
      const res = await fetch(`/api/analyses/${analysisId}/toggle-public`, {
        method: 'POST',
      })

      if (!res.ok) {
        const errData = await res.json()
        throw new Error(errData.error || 'Failed to update analysis visibility.')
      }

      const result = await res.json()
      
      // Update local state
      setAnalysis((prev: any) => prev ? { ...prev, is_public: result.analysis.is_public } : prev)
      
      setTogglePublicLoading(false)
      // Show success message briefly
      setTimeout(() => setError(''), 3000)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update visibility.')
      setTogglePublicLoading(false)
    }
  }

  const handleShare = () => {
    setShowShareModal(true)
  }

  const getShareUrl = () => {
    if (!analysis) return ''
    return `${window.location.origin}/ad/${analysis.slug || analysis.id}`
  }

  const getShareText = () => {
    if (!analysis) return ''
    const adTitle = analysis.video_title || analysis.title || `${analysis.inferred_brand || 'Brand'} Ad`
    return `Check out this AI analysis of "${adTitle}" on`
  }

  const shareToTwitter = () => {
    const text = encodeURIComponent(getShareText())
    const url = encodeURIComponent(getShareUrl())
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`, '_blank')
  }

  const shareToLinkedIn = () => {
    const url = encodeURIComponent(getShareUrl())
    const title = encodeURIComponent(`${analysis?.inferred_brand || 'Brand'} Ad Analysis - AdBreakdown`)
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}&title=${title}`, '_blank')
  }

  const shareToWhatsApp = () => {
    const text = encodeURIComponent(`${getShareText()} ${getShareUrl()}`)
    window.open(`https://wa.me/?text=${text}`, '_blank')
  }

  const shareToInstagram = () => {
    // Instagram doesn't have a direct web sharing API, so we copy to clipboard and show instructions
    copyToClipboard()
    alert('Link copied! Open Instagram and paste the link in your story or post.')
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(getShareUrl())
      alert('Link copied to clipboard!')
    } catch (error) {
      console.error('Clipboard error:', error)
      // Fallback: show the URL to copy manually
      prompt('Copy this link:', getShareUrl())
    }
  }

  const formatGeneratedContent = (_: string, content: any): string => {
    if (!content) return 'No content generated.'
    // This function can be expanded to format different report types
    return content.raw_content || content.scorecard_table || JSON.stringify(content, null, 2)
  }

  // Render Logic
  if (authLoading) {
    return <div className="min-h-screen flex items-center justify-center"><p>Loading authentication...</p></div>
  }
  
  // Allow public access if analysis is public, otherwise require authentication
  const isTestingMode = process.env.NODE_ENV === 'development'
  const isPublicAnalysis = analysis?.is_public === true
  
  if (!isAuthenticated && !isTestingMode && !isPublicAnalysis && !isFeaturedMode) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-[calc(100vh-64px)]">
          <div className="text-center">
            <p className="mb-4">Please sign in to view this analysis.</p>
            <Link href="/sign-in"><Button>Sign In</Button></Link>
          </div>
        </div>
      </div>
    )
  }
  if (isInitialLoading) {
    return <LoadingSkeleton />
  }

  if (loading && !analysis) {
    return <div className="min-h-screen flex items-center justify-center"><p>Loading analysis data...</p></div>
  }

  // If analysis is null after loading, it means no analysis exists for this ID
  if (!analysis && !loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-[calc(100vh-64px)]">
          <Card className="mb-8 text-center">
            <CardHeader>
              <CardTitle>Analysis Not Found</CardTitle>
              <CardDescription>No analysis found for ID: {analysisId}.</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/dashboard"><Button>Back to Dashboard</Button></Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <main className="container mx-auto px-4 py-4 max-w-6xl">
        <ErrorDisplay error={error} />

        {!isFeaturedMode && (
          <>
            <DeleteAnalysisModal 
              isOpen={showDeleteConfirm}
              onClose={() => setShowDeleteConfirm(false)}
              onConfirm={handleDeleteAnalysis}
              isDeleting={isDeleting}
            />

            <ShareAnalysisModal 
              isOpen={showShareModal}
              onClose={() => setShowShareModal(false)}
              analysis={analysis}
              shareHandlers={{
                shareToTwitter,
                shareToLinkedIn,
                shareToWhatsApp,
                shareToInstagram,
                copyToClipboard
              }}
            />
          </>
        )}

        {console.log('Rendering video/campaign analysis components. analysis:', analysis, 'videoMetadata:', videoMetadata, 'youtubeMetadata:', youtubeMetadata)}
        {analysis && (analysis.video_url || youtubeVideoId) && (
          <div className="mb-3">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl font-semibold text-gray-900 truncate overflow-hidden">{youtubeMetadata?.title || videoMetadata.title}</h1>
              </div>
              {!isFeaturedMode && (
                <div className="flex items-center gap-2 shrink-0">
                  {/* Publish/Unpublish Toggle Button */}
                  {isAuthenticated && analysis && (
                    <Button
                      onClick={togglePublicStatus}
                      variant={analysis.is_public ? "outline" : "default"}
                      size="sm"
                      disabled={togglePublicLoading}
                      className={`flex items-center gap-2 ${
                        analysis.is_public 
                          ? "border-red-300 text-red-600 hover:bg-red-50 hover:text-red-700" 
                          : "bg-green-600 hover:bg-green-700 text-white"
                      }`}
                    >
                      {analysis.is_public ? (
                        <>
                          <Globe className="h-4 w-4" />
                          Unpublish
                        </>
                      ) : (
                        <>
                          <Globe className="h-4 w-4" />
                          Publish
                        </>
                      )}
                    </Button>
                  )}
                  {/* Share Button */}
                  <Button
                    onClick={handleShare}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Share2 className="h-4 w-4" />
                    Share
                  </Button>
                </div>
              )}
            </div>
            {/* Badges - Full width below title and buttons */}
            <div className="flex flex-wrap gap-2 mt-3">
              {getBadges().map((badge, index) => (
                <Badge key={index} variant={badge.variant} className="bg-blue-100 text-blue-800 border-blue-200">
                  {badge.label}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Skip step 1 - pending analysis card is disabled */}

        {analysis?.status === 'processing' && (
          <ProcessingStatusCard />
        )}

        {(analysis?.status === 'completed' || analysis?.status === 'generated' || analysis?.status === 'pending') && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content Column - 3/4 width */}
            <div className="lg:col-span-3 space-y-8">
              {/* HERO SECTION */}
              <section>
                {analysis && (analysis.video_url || youtubeVideoId) && (
                  <div className="w-full aspect-video mb-8">
                    <VideoPlayer 
                      showVideo={showVideo}
                      youtubeVideoId={youtubeVideoId}
                      videoMetadata={videoMetadata}
                      onShowVideo={() => setShowVideo(true)}
                    />
                  </div>
                )}
              </section>
              
              {/* EXECUTIVE BRIEFING SECTION */}
              <section>
                <ExecutiveBriefingCard parsedData={parsedData} />
              </section>
              
              {/* CORE ANALYSIS SECTION */}
              <section>
                <CoreAnalysisCard parsedData={parsedData} />
              </section>
              
              {/* DIAGNOSIS SECTION */}
              <section>
                <DiagnosisCard parsedData={parsedData} />
              </section>
              
              {/* DEEP DIVE SECTION */}
              <section className="space-y-6">
                <div className="bg-white rounded-xl p-8 shadow-md">
                  <h2 className="text-3xl font-bold text-gray-900 mb-6 flex items-center">
                    <BarChart3 className="w-8 h-8 mr-3 text-blue-600" />
                    The Evidence Locker: A Strategic Deep Dive
                  </h2>
                  
                  <TabsComponent parsedData={parsedData} />
                </div>
              </section>
              
              {/* Generated Content Sections */}
              {!isFeaturedMode && (
                <GeneratedContentSections
                  marketingCopy={marketingCopy}
                  socialMediaPosts={socialMediaPosts}
                  marketingScorecard={marketingScorecard}
                  seoKeywords={seoKeywords}
                  contentSuggestions={contentSuggestions}
                />
              )}
            </div>
            
            {/* SIDEBAR: SUPPORTING DATA */}
            <div className="lg:col-span-1">
              <div className="lg:sticky lg:top-6 space-y-6">
                {/* Scorecard Sidebar Module */}
                <ScorecardSidebar parsedData={parsedData} />
                
                {/* Metadata Sidebar Module */}
                <MetadataSidebar 
                  parsedData={parsedData} 
                  youtubeMetadata={youtubeMetadata}
                  videoMetadata={videoMetadata}
                />
                
                {/* Generate Insights Sidebar Module */}
                {!isFeaturedMode && (
                  <div className="bg-white rounded-xl p-6 shadow-md">
                    <h3 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                      <Lightbulb className="w-5 h-5 mr-2 text-blue-600" />
                      Generate More Insights
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      Use your credits to generate additional marketing assets and analysis based on this video.
                    </p>
                    <div className="space-y-3">
                      <Button
                        onClick={() => generateFeature('marketing_copy', setMarketingCopy, setCopyLoading)}
                        disabled={copyLoading || loading || !!marketingCopy}
                        className="w-full text-sm"
                        size="sm"
                      >
                        {marketingCopy ? 'Copy Generated' : (copyLoading ? 'Generating...' : 'Generate Marketing Copy (1 Credit)')}
                      </Button>
                      <Button
                        onClick={() => generateFeature('social_media_posts', setSocialMediaPosts, setSocialLoading)}
                        disabled={socialLoading || loading || !!socialMediaPosts}
                        className="w-full text-sm"
                        size="sm"
                      >
                        {socialMediaPosts ? 'Posts Generated' : (socialLoading ? 'Generating...' : 'Propose Social Posts (1 Credit)')}
                      </Button>
                      <Button
                        onClick={() => generateFeature('marketing_scorecard', setMarketingScorecard, setScorecardLoading)}
                        disabled={scorecardLoading || loading || !!marketingScorecard}
                        className="w-full text-sm"
                        size="sm"
                      >
                        {marketingScorecard ? 'Scorecard Generated' : (scorecardLoading ? 'Generating...' : 'Generate Scorecard (1 Credit)')}
                      </Button>
                      <Button
                        onClick={() => generateFeature('seo_keywords', setSeoKeywords, setKeywordsLoading)}
                        disabled={keywordsLoading || loading || !!seoKeywords}
                        className="w-full text-sm"
                        size="sm"
                      >
                        {seoKeywords ? 'Keywords Generated' : (keywordsLoading ? 'Generating...' : 'Extract SEO Keywords (1 Credit)')}
                      </Button>
                      <Button
                        onClick={() => generateFeature('content_suggestions', setContentSuggestions, setSuggestionsLoading)}
                        disabled={suggestionsLoading || loading || !!contentSuggestions}
                        className="w-full text-sm"
                        size="sm"
                      >
                        {contentSuggestions ? 'Suggestions Generated' : (suggestionsLoading ? 'Generating...' : 'Suggest Improvements (1 Credit)')}
                      </Button>
                    </div>
                  </div>
                )}
                
                {/* Share Analysis */}
                {!isFeaturedMode && (
                  <div className="bg-white rounded-xl p-6 shadow-md">
                    <h3 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                      <Share2 className="w-5 h-5 mr-2 text-green-600" />
                      Share this Analysis
                    </h3>
                    <Button
                      onClick={handleShare}
                      className="w-full"
                      variant="outline"
                    >
                      Share Analysis
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </main>
      
      {/* Login Overlay for unauthenticated users viewing public analyses */}
      {showLoginOverlay && !isAuthenticated && analysis?.is_public && !isFeaturedMode && (
        <LoginOverlay 
          isVisible={showLoginOverlay}
          title="Unlock Full Analysis"
          description="Sign up to access the complete analysis with detailed insights, AI-powered recommendations, and advanced features."
        />
      )}
    </div>
  )
}

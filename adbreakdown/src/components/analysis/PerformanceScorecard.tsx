'use client'

import React from 'react'
import { BarChart3, Target, Lightbulb, Heart, Zap, Award, Brain } from 'lucide-react'
import ScoreCard from './ScoreCard'

interface ScorecardData {
  overall_impact_score: number
  strategic_foundation: number
  creative_execution: number
  emotional_resonance: number
  cta_clarity: number
  brand_integration: number
  memorability_factor: number
}

interface PerformanceScorecardProps {
  scorecard: ScorecardData
}

export default function PerformanceScorecard({ scorecard }: PerformanceScorecardProps) {
  if (!scorecard) return null

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
        <BarChart3 className="w-6 h-6 mr-2 text-blue-600" />
        Performance Scorecard
      </h2>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Overall Score - spans 2 rows in col 1 */}
        <div className="row-span-2 flex flex-col items-center justify-center bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="text-center">
            <div className="relative w-24 h-24 mx-auto mb-3">
              <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 120 120">
                <circle cx="60" cy="60" r="50" fill="none" stroke="#e5e7eb" strokeWidth="8"/>
                <circle 
                  cx="60" cy="60" r="50" fill="none" 
                  stroke="#10B981" strokeWidth="8"
                  strokeLinecap="round"
                  strokeDasharray={`${(scorecard.overall_impact_score || 0) * 31.4}, 314`}
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{scorecard.overall_impact_score || 0}</div>
                  <div className="text-xs text-gray-600">Overall</div>
                </div>
              </div>
            </div>
            <h3 className="text-sm font-semibold text-gray-900">Overall Impact Score</h3>
            <p className="text-xs text-gray-600">Expert analysis rating</p>
          </div>
        </div>
        
        {/* 6 Score Cards in next 3 columns, 2 rows */}
        <div className="col-span-1">
          <ScoreCard label="Strategic Foundation" score={scorecard.strategic_foundation} icon={Target} color="blue" />
        </div>
        <div className="col-span-1">
          <ScoreCard label="Creative Execution" score={scorecard.creative_execution} icon={Lightbulb} color="purple" />
        </div>
        <div className="col-span-1">
          <ScoreCard label="Emotional Resonance" score={scorecard.emotional_resonance} icon={Heart} color="red" />
        </div>
        <div className="col-span-1">
          <ScoreCard label="Call-to-Action" score={scorecard.cta_clarity} icon={Zap} color="yellow" />
        </div>
        <div className="col-span-1">
          <ScoreCard label="Brand Integration" score={scorecard.brand_integration} icon={Award} color="green" />
        </div>
        <div className="col-span-1">
          <ScoreCard label="Memorability" score={scorecard.memorability_factor} icon={Brain} color="indigo" />
        </div>
      </div>
    </div>
  )
}
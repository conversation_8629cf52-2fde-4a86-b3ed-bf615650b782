'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTit<PERSON> } from '@/components/ui/card'
import { AlertTriangle, CheckCircle, Lightbulb } from 'lucide-react'

interface DiagnosisCardProps {
  parsedData: any
}

export default function DiagnosisCard({ parsedData }: DiagnosisCardProps) {
  const diagnosis = parsedData?.diagnosis

  if (!diagnosis) {
    return null
  }

  // Check if there are any pitfalls
  const hasPitfalls = diagnosis.emotional_connection?.pitfall || 
                     diagnosis.strategic_sense?.pitfall || 
                     diagnosis.clarity_of_whats_next?.pitfall

  if (!hasPitfalls) {
    // Show positive message when no issues detected
    return (
      <Card className="shadow-sm border border-gray-200 bg-white">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-gray-500" />
            Strong Strategic Cohesion
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <p className="text-gray-700 leading-relaxed text-sm">
              No major issues detected. This ad demonstrates effective emotional engagement, 
              clear strategic direction, and appropriate next steps for viewers.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2 text-gray-500" />
          The Diagnosis: Issues & Solutions
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Emotional Connection */}
          {diagnosis.emotional_connection?.pitfall && (
            <div className="border border-gray-200 rounded-lg p-4 bg-white">
              <h3 className="text-base font-semibold text-gray-900 mb-3 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                Emotional Connection Issue
              </h3>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2 text-sm">The Problem:</h4>
                  <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.emotional_connection.pitfall}</p>
                </div>
                {diagnosis.emotional_connection.improvement_signal && (
                  <div className="border-t border-gray-200 pt-3">
                    <h4 className="font-medium text-gray-800 mb-2 flex items-center text-sm">
                      <Lightbulb className="w-4 h-4 mr-1" />
                      The Solution:
                    </h4>
                    <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.emotional_connection.improvement_signal}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Strategic Sense */}
          {diagnosis.strategic_sense?.pitfall && (
            <div className="border border-gray-200 rounded-lg p-4 bg-white">
              <h3 className="text-base font-semibold text-gray-900 mb-3 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                Strategic Alignment Issue
              </h3>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2 text-sm">The Problem:</h4>
                  <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.strategic_sense.pitfall}</p>
                </div>
                {diagnosis.strategic_sense.improvement_signal && (
                  <div className="border-t border-gray-200 pt-3">
                    <h4 className="font-medium text-gray-800 mb-2 flex items-center text-sm">
                      <Lightbulb className="w-4 h-4 mr-1" />
                      The Solution:
                    </h4>
                    <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.strategic_sense.improvement_signal}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Clarity of What's Next */}
          {diagnosis.clarity_of_whats_next?.pitfall && (
            <div className="border border-gray-200 rounded-lg p-4 bg-white">
              <h3 className="text-base font-semibold text-gray-900 mb-3 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                Call-to-Action Issue
              </h3>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-gray-800 mb-2 text-sm">The Problem:</h4>
                  <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.clarity_of_whats_next.pitfall}</p>
                </div>
                {diagnosis.clarity_of_whats_next.improvement_signal && (
                  <div className="border-t border-gray-200 pt-3">
                    <h4 className="font-medium text-gray-800 mb-2 flex items-center text-sm">
                      <Lightbulb className="w-4 h-4 mr-1" />
                      The Solution:
                    </h4>
                    <p className="text-gray-700 leading-relaxed text-sm">{diagnosis.clarity_of_whats_next.improvement_signal}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    font-family: var(--font-inter); /* Default font */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.font-caprasimo {
  font-family: var(--font-caprasimo);
}

/* Enhanced Script Container Styling */
@layer components {
  .enhanced-script-content {
    @apply max-w-none;
    line-height: 1.7;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
  
  .enhanced-script-content h1 {
    @apply scroll-mt-20;
  }
  
  .enhanced-script-content h2 {
    @apply scroll-mt-20;
  }
  
  .enhanced-script-content h3 {
    @apply scroll-mt-20;
  }
  
  .enhanced-script-content h4 {
    @apply scroll-mt-20;
  }
  
  .enhanced-script-content p {
    @apply text-justify;
  }
  
  .enhanced-script-content ul li {
    @apply text-justify;
  }
  
  .enhanced-script-content ol li {
    @apply text-justify;
  }
  
  /* Better spacing for nested lists */
  .enhanced-script-content li ul,
  .enhanced-script-content li ol {
    @apply mt-2 mb-2;
  }
  
  /* Improved code block styling */
  .enhanced-script-content pre {
    overflow-x: auto;
  }
  
  /* Better table styling */
  .enhanced-script-content table {
    @apply shadow-sm;
  }
  
  /* Improved blockquote styling */
  .enhanced-script-content blockquote {
    @apply shadow-sm;
  }

  .generated-content {
    @apply text-sm font-normal leading-relaxed tracking-normal;
  }

  .generated-content table {
    @apply w-full border-collapse shadow-sm;
  }

  .generated-content .table-container {
    @apply rounded-lg overflow-hidden;
  }

  .generated-content th,
  .generated-content td {
    @apply border border-gray-200 p-3 text-left;
  }

  .generated-content th {
    @apply bg-gray-100 font-semibold p-4 whitespace-nowrap;
  }

  .generated-content tbody tr:nth-child(even) {
    @apply bg-gray-50;
  }

  /* Custom emoji and icon styling */
  .enhanced-script-content h1 .emoji,
  .enhanced-script-content h2 .emoji,
  .enhanced-script-content h3 .emoji,
  .enhanced-script-content h4 .emoji {
    @apply inline-block mr-2;
  }
}
import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { getEnhancedScriptPrompt, enhancedScriptSystemInstruction } from '@/lib/prompts/enhancedScriptPrompt'

export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // Verify ownership and get analysis data
    const { data: adAnalysis, error: ownerError } = await supabase
      .from('ad_analyses')
      .select('id, user_id, youtube_url, status, title, deciphered_script')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    if (ownerError || !adAnalysis) {
      return NextResponse.json({ error: 'Analysis not found' }, { status: 404 })
    }

    if (adAnalysis.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    if (adAnalysis.status !== 'completed') {
      return NextResponse.json({ 
        error: 'Analysis must be completed before generating enhanced script' 
      }, { status: 400 })
    }

    // Check user credits
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('credits_remaining')
      .eq('user_id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    const creditCost = 3
    if (profile.credits_remaining < creditCost) {
      return NextResponse.json({ error: 'Insufficient credits' }, { status: 402 })
    }

    // Check if enhanced script already exists
    if (adAnalysis.deciphered_script?.enhanced_analysis) {
      return NextResponse.json({
        message: 'Enhanced script already generated',
        content: {
          enhanced_script: adAnalysis.deciphered_script.enhanced_analysis,
          model: adAnalysis.deciphered_script.enhanced_analysis_model || 'gemini-2.5-pro'
        }
      }, { status: 200 })
    }

    // Generate enhanced script using Gemini 2.5 Pro
    try {
      console.log('🎬 Starting enhanced script generation for analysis:', id)

      const enhancedScriptPrompt = getEnhancedScriptPrompt(adAnalysis.youtube_url)

      const apiKey = process.env.GEMINI_API_KEY || "AIzaSyBoI6_LcEKmsmLVJXE7Q_Fe72WmwlqOL9M"
      
      const payload = {
        contents: [{
          role: "user",
          parts: [
            { text: enhancedScriptPrompt },
            { fileData: { mimeType: "video/mp4", fileUri: adAnalysis.youtube_url } }
          ]
        }],
        systemInstruction: {
          parts: [{
            text: enhancedScriptSystemInstruction
          }]
        },
        generationConfig: {
          temperature: 1.0,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      }

      const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent?key=${apiKey}`

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`Gemini API Error: ${errorData.error?.message || response.statusText}`)
      }

      const result = await response.json()
      
      if (!result.candidates || result.candidates.length === 0 ||
          !result.candidates[0].content || !result.candidates[0].content.parts ||
          result.candidates[0].content.parts.length === 0) {
        throw new Error("Could not get valid response from Gemini.")
      }

      const enhancedScript = result.candidates[0].content.parts[0].text

      // Create the enhanced script object
      const updatedScript = {
        enhanced_analysis: enhancedScript,
        enhanced_analysis_model: 'gemini-2.5-pro',
        enhanced_analysis_generated_at: new Date().toISOString()
      }

      const { error: updateError } = await supabase
        .from('ad_analyses')
        .update({
          deciphered_script: updatedScript,
          updated_at: new Date().toISOString()
        })
        .eq('id', adAnalysis.id)

      if (updateError) {
        throw new Error(`Failed to save enhanced script: ${updateError.message}`)
      }

      console.log('✅ Enhanced script analysis completed successfully:', id)

      // Deduct credits
      const { error: creditError } = await supabase.rpc('decrement_credits', {
        user_id_param: user.id,
        amount: creditCost,
      })

      if (creditError) {
        console.error('Failed to deduct credits:', creditError)
      }

      return NextResponse.json({ 
        success: true, 
        message: 'Enhanced script generated successfully',
        content: {
          enhanced_script: enhancedScript,
          model: 'gemini-2.5-pro'
        }
      }, { status: 200 })

    } catch (generationError) {
      console.error('❌ Error during enhanced script generation:', generationError)

      return NextResponse.json({ 
        error: 'Failed to generate enhanced script',
        message: generationError instanceof Error ? generationError.message : 'Unknown error'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error generating enhanced script:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
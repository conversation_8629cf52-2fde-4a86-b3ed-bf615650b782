
import { NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// IMPORTANT: Do not expose your API key on the client-side.
// Use environment variables to store your API key.
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");

async function callGeminiApi(prompt: string, isJsonResponse = false) {
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    const generationConfig = isJsonResponse ? { responseMimeType: "application/json" } : {};

    const result = await model.generateContent(prompt, generationConfig);
    const response = await result.response;
    const text = response.text();
    return text;
}

export async function POST(request: Request) {
  try {
    const { youtubeUrl } = await request.json();

    if (!youtubeUrl) {
      return NextResponse.json({ error: 'YouTube URL is required' }, { status: 400 });
    }

    // This prompt is taken from the prototype and adapted for the backend
    const videoAnalysisPrompt = `
        Analyze this YouTube video from the URL: ${youtubeUrl}.
        Provide a detailed transcript of the spoken content and a concise summary
        of what the video is about and what is happening in it.
        From the video content, infer the video's main title and any explicit or strongly implied brand name.
        Also, provide inferred key themes, a general emotional tone (e.g., positive, neutral, negative) and associated emotions (joy, sadness, anger, surprise),
        music mood, voice tone, and general targeting recommendations (demographics, interests, behaviors).
        Estimate the video's duration.

        Format your response as a JSON object with the following structure:
        {
            "title": "Inferred Video Title",
            "inferredBrandName": "Inferred Brand Name",
            "duration": "MM:SS",
            "transcript": "Full video transcript...",
            "summary": "Concise summary...",
            "overallSentiment": 0.0, // Score from -1.0 to 1.0
            "emotions": {"joy": 0, "sadness": 0, "anger": 0, "fear": 0, "surprise": 0, "disgust": 0}, // Percentages
            "keyThemes": ["theme1", "theme2"],
            "musicMood": "Inferred Music Mood",
            "voiceTone": "Inferred Voice Tone",
            "targetingRecommendations": {
                "demographics": ["demographic1"],
                "interests": ["interest1"],
                "behaviors": ["behavior1"]
            }
        }
    `;

    const analysisResult = await callGeminiApi(videoAnalysisPrompt, true);
    const analysisData = JSON.parse(analysisResult);

    // Now, get the marketing analysis
    const marketingAnalysisPrompt = `
        You are a marketing expert and brand marketing expert with 20 years of experience in ad creation, creative agency lead, and video production.
        Analyze the following video content and provide an expert analysis with the following structured output.
        The video content information (transcript and summary) is:

        ---
        ${analysisData.transcript}\n\nSummary: ${analysisData.summary}
        ---

        Provide the analysis using this framework:

        1.  **General Ad Impression & Core Concept:**
        2.  **Dissection of the Target User Profile:** (Who is the ad trying to reach? Demographics, psychographics, interests.)
        3.  **Problem Addressed/Pain Point:** (What problem or need does the ad acknowledge or create? Both explicit and implicit.)
        4.  **Brand Positioning:** (How is the brand portrayed in relation to competitors or the market? Explicit and implicit.)
        5.  **Solution Offered (Explicit & Implicit):** (What does the ad offer as a solution?)
        6.  **Brand Highlight / Unique Selling Proposition (USP):** (What makes the brand or its message unique?)
        7.  **Creative Strategy & Execution:** (Analyze tone, humor, visuals, sound design, pacing, storytelling.)
        8.  **Call to Action (CTA):** (What does the ad want the viewer to do? Explicit and implicit.)
        9.  **Effectiveness & Potential Impact:** (How well is it likely to achieve its goals? Strengths.)
        10. **Areas for Improvement / Further Considerations:** (Any weaknesses or missed opportunities?)

        Ensure your analysis is professional, insightful, and reflects a deep understanding of marketing principles.
    `;

    const marketingAnalysis = await callGeminiApi(marketingAnalysisPrompt);

    return NextResponse.json({ ...analysisData, marketingAnalysis });

  } catch (error) {
    console.error('Error analyzing video:', error);
    return NextResponse.json({ error: 'Failed to analyze video' }, { status: 500 });
  }
}

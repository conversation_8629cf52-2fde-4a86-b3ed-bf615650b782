// Supabase Edge Function: run-llm-feature-generation
// This function handles LLM-powered feature generation (marketing copy, social posts, etc.)

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GoogleGenerativeAI } from 'https://esm.sh/@google/generative-ai@0.1.3'

interface FeatureRequest {
  analysis_id: string
  report_id: string
  report_type_name: string
  user_id: string
  credit_cost: number
}

serve(async (req) => {
  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Initialize Gemini AI
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY')!
    const genAI = new GoogleGenerativeAI(geminiApiKey)
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

    // Parse request
    const { analysis_id, report_id, report_type_name, user_id, credit_cost }: FeatureRequest = await req.json()

    console.log('Processing LLM feature generation:', { analysis_id, report_type_name, user_id })

    // Get existing analysis reports for context
    const { data: existingReports, error: reportsError } = await supabase
      .from('analysis_reports')
      .select(`
        content,
        report_types(name)
      `)
      .eq('analysis_id', analysis_id)
      .eq('status', 'generated')
      .in('report_types.name', ['transcript_summary', 'marketing_analysis'])

    if (reportsError) {
      console.error('Error fetching existing reports:', reportsError)
      throw new Error('Failed to fetch context reports')
    }

    // Extract context data
    const transcriptReport = existingReports.find(r => r.report_types?.name === 'transcript_summary')
    const marketingReport = existingReports.find(r => r.report_types?.name === 'marketing_analysis')

    if (!transcriptReport || !marketingReport) {
      throw new Error('Required context reports not found')
    }

    const transcriptData = transcriptReport.content
    const marketingData = marketingReport.content

    // Generate content based on report type
    let prompt: string
    let generatedContent: any

    switch (report_type_name) {
      case 'marketing_copy':
        prompt = `
Based on the following video analysis:

Transcript: ${transcriptData.transcript}
Summary: ${transcriptData.summary}
Themes: ${transcriptData.themes?.join(', ')}

Marketing Analysis: ${JSON.stringify(marketingData, null, 2)}

Generate compelling marketing copy including:
1. 5 attention-grabbing headlines
2. 3 short marketing paragraphs (2-3 sentences each)
3. Key selling points

Make the copy engaging and suitable for various marketing channels.
`
        break

      case 'social_media_posts':
        prompt = `
Based on the following video analysis:

Transcript: ${transcriptData.transcript}
Summary: ${transcriptData.summary}
Themes: ${transcriptData.themes?.join(', ')}

Create 4 social media posts for different platforms:
1. Twitter/X (concise, under 280 characters)
2. LinkedIn (professional, business-focused)
3. Instagram (engaging with emojis and hashtags)
4. Facebook (conversational and shareable)

Each post should capture the essence of the video and encourage engagement.
`
        break

      case 'marketing_scorecard':
        prompt = `
Based on the marketing analysis:

${JSON.stringify(marketingData, null, 2)}

Create a marketing scorecard in this exact format:

| Parameter | Score (1-5) | Justification |
|-----------|-------------|---------------|
| Message Clarity | X | Brief explanation |
| Emotional Appeal | X | Brief explanation |
| Visual Appeal | X | Brief explanation |
| Call to Action | X | Brief explanation |
| Brand Consistency | X | Brief explanation |
| Target Audience Fit | X | Brief explanation |
| Overall Effectiveness | X | Brief explanation |

Provide scores from 1-5 and concise justifications.
`
        break

      case 'seo_keywords':
        prompt = `
Based on the video content:

Transcript: ${transcriptData.transcript}
Summary: ${transcriptData.summary}
Themes: ${transcriptData.themes?.join(', ')}

Extract 15-20 SEO keywords and phrases:
1. Primary keywords (5-7 main terms)
2. Long-tail keywords (8-10 specific phrases)
3. Intent-based keywords (3-5 action-oriented terms)

For each keyword, briefly explain why it's relevant to the content.
`
        break

      case 'content_suggestions':
        prompt = `
Based on the marketing analysis:

${JSON.stringify(marketingData, null, 2)}

Provide 5-7 specific, actionable content improvement suggestions:

1. Structure and Flow improvements
2. Messaging enhancements
3. Visual recommendations
4. Audio/voice improvements
5. Call-to-action optimization
6. Targeting refinements
7. Overall impact boosters

Make each suggestion practical and implementable.
`
        break

      default:
        throw new Error(`Unsupported report type: ${report_type_name}`)
    }

    // Generate content using Gemini
    const result = await model.generateContent(prompt)
    const response = result.response
    const generatedText = response.text()

    // Structure the content based on type
    switch (report_type_name) {
      case 'marketing_copy':
        generatedContent = {
          headlines: extractSection(generatedText, 'headlines', 5),
          paragraphs: extractSection(generatedText, 'paragraphs', 3),
          raw_content: generatedText
        }
        break
      
      case 'social_media_posts':
        generatedContent = {
          twitter: extractPlatformPost(generatedText, 'twitter'),
          linkedin: extractPlatformPost(generatedText, 'linkedin'),
          instagram: extractPlatformPost(generatedText, 'instagram'),
          facebook: extractPlatformPost(generatedText, 'facebook'),
          raw_content: generatedText
        }
        break
      
      case 'marketing_scorecard':
        generatedContent = {
          scorecard_table: generatedText,
          scores: extractScores(generatedText),
          raw_content: generatedText
        }
        break
      
      case 'seo_keywords':
        generatedContent = {
          primary_keywords: extractKeywordSection(generatedText, 'primary'),
          long_tail_keywords: extractKeywordSection(generatedText, 'long-tail'),
          intent_keywords: extractKeywordSection(generatedText, 'intent'),
          raw_content: generatedText
        }
        break
      
      case 'content_suggestions':
        generatedContent = {
          suggestions: extractSuggestions(generatedText),
          raw_content: generatedText
        }
        break

      default:
        generatedContent = { raw_content: generatedText }
    }

    // Update the report with generated content
    const { error: updateError } = await supabase
      .from('analysis_reports')
      .update({
        content: generatedContent,
        status: 'generated',
        generated_at: new Date().toISOString()
      })
      .eq('id', report_id)

    if (updateError) {
      console.error('Error updating report:', updateError)
      throw new Error('Failed to save generated content')
    }

    // Credit deduction disabled for testing
    console.log(`🧪 Testing mode: Skipping credit deduction for ${report_type_name}`)
    // const { error: creditError } = await supabase
    //   .from('profiles')
    //   .update({
    //     credits_remaining: supabase.rpc('decrement_credits', { 
    //       user_id_param: user_id, 
    //       cost: credit_cost 
    //     })
    //   })
    //   .eq('user_id', user_id)

    // if (creditError) {
    //   console.error('Error deducting credits:', creditError)
    //   // Log credit usage for manual reconciliation
    //   await supabase
    //     .from('credit_usage')
    //     .insert({
    //       user_id,
    //       analysis_id,
    //       report_type_id: await getReportTypeId(supabase, report_type_name),
    //       credits_used: credit_cost,
    //       description: `Generated ${report_type_name} for analysis ${analysis_id}`
    //     })
    // }

    console.log('LLM feature generation completed:', { analysis_id, report_type_name })

    return new Response(
      JSON.stringify({ 
        success: true, 
        report_id,
        content_preview: generatedText.substring(0, 200) + '...',
        credits_used: credit_cost
      }),
      { 
        headers: { 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Error in run-llm-feature-generation:', error)
    
    // Mark report as error if we have the report_id
    try {
      const { report_id } = await req.json()
      if (report_id) {
        const supabaseUrl = Deno.env.get('SUPABASE_URL')!
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
        const supabase = createClient(supabaseUrl, supabaseServiceKey)
        
        await supabase
          .from('analysis_reports')
          .update({ status: 'error' })
          .eq('id', report_id)
      }
    } catch (updateError) {
      console.error('Error updating report status to error:', updateError)
    }

    return new Response(
      JSON.stringify({ 
        error: 'Feature generation failed', 
        message: error.message 
      }),
      { 
        headers: { 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

// Helper functions for content extraction
function extractSection(text: string, sectionType: string, count: number): string[] {
  // Simple extraction - in production, use more sophisticated parsing
  const lines = text.split('\n').filter(line => line.trim())
  const startIndex = lines.findIndex(line => 
    line.toLowerCase().includes(sectionType.toLowerCase())
  )
  
  if (startIndex === -1) return []
  
  return lines.slice(startIndex + 1, startIndex + 1 + count)
    .filter(line => line.trim())
    .map(line => line.replace(/^\d+\.\s*/, '').trim())
}

function extractPlatformPost(text: string, platform: string): string {
  const lines = text.split('\n')
  const platformIndex = lines.findIndex(line => 
    line.toLowerCase().includes(platform.toLowerCase())
  )
  
  if (platformIndex === -1) return ''
  
  // Get the next non-empty line after the platform heading
  for (let i = platformIndex + 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (line && !line.includes(':') && line.length > 20) {
      return line
    }
  }
  
  return ''
}

function extractScores(text: string): Record<string, number> {
  const scores: Record<string, number> = {}
  const lines = text.split('\n')
  
  lines.forEach(line => {
    const match = line.match(/\|\s*([^|]+)\s*\|\s*(\d+)\s*\|/)
    if (match) {
      const parameter = match[1].trim().toLowerCase().replace(/\s+/g, '_')
      const score = parseInt(match[2])
      if (!isNaN(score) && score >= 1 && score <= 5) {
        scores[parameter] = score
      }
    }
  })
  
  return scores
}

function extractKeywordSection(text: string, type: string): string[] {
  const lines = text.split('\n')
  const typeIndex = lines.findIndex(line => 
    line.toLowerCase().includes(type.toLowerCase())
  )
  
  if (typeIndex === -1) return []
  
  const keywords: string[] = []
  for (let i = typeIndex + 1; i < lines.length && keywords.length < 10; i++) {
    const line = lines[i].trim()
    if (line && (line.startsWith('-') || line.match(/^\d+\./))) {
      keywords.push(line.replace(/^[-\d.\s]+/, '').trim())
    } else if (line && !line.includes(':') && keywords.length === 0) {
      break
    }
  }
  
  return keywords
}

function extractSuggestions(text: string): string[] {
  const lines = text.split('\n')
  const suggestions: string[] = []
  
  lines.forEach(line => {
    const trimmed = line.trim()
    if (trimmed && (trimmed.match(/^\d+\./) || trimmed.startsWith('-'))) {
      suggestions.push(trimmed.replace(/^[-\d.\s]+/, '').trim())
    }
  })
  
  return suggestions
}

async function getReportTypeId(supabase: any, reportTypeName: string): Promise<number> {
  const { data } = await supabase
    .from('report_types')
    .select('id')
    .eq('name', reportTypeName)
    .single()
  
  return data?.id || 0
}

// Supabase Edge Function: run-ad-analysis (UPDATED VERSION)
// This function handlesi d comprehensive AI-powered video analysis using Gemini API
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { GoogleGenerativeAI } from 'https://esm.sh/@google/generative-ai@0.1.3';
serve(async (req)=>{
  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Initialize Gemini AI with 1.5 Pro for better analysis
    const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
    const genAI = new GoogleGenerativeAI(geminiApiKey);
    // Initialize model without the unsupported parameters
    const model = genAI.getGenerativeModel({
      model: 'gemini-1.5-pro'
    });
    // Parse request
    const { analysis_id, user_id, youtubeUrl } = await req.json();
    console.log('Processing comprehensive analysis:', {
      analysis_id,
      user_id,
      youtubeUrl
    });
    // Update analysis status to processing
    await supabase.from('ad_analyses').update({
      status: 'processing'
    }).eq('id', analysis_id);
    // For now, create mock data since direct YouTube analysis isn't implemented
    // TODO: Implement proper YouTube video download and upload to Gemini File API
    console.log('Creating mock analysis data for YouTube URL:', youtubeUrl);
    
    const basicInfo = {
      transcript: "This is a mock transcript for testing purposes. The actual implementation would extract the real video transcript.",
      summary: "Mock summary: This appears to be a video advertisement that demonstrates product features and benefits to engage viewers.",
      duration_seconds: 30,
      thumbnail_description: "Video thumbnail showing the main product or brand element"
    };
    
    console.log('Mock basic info created:', basicInfo);
    // Now run the comprehensive veteran analysis
    // We'll modify the prompt to explicitly request JSON format since we can't use responseSchema
    const comprehensivePrompt = `
# The Ad Analyst's Eye: Expert-Level Video Ad Breakdown

You're the industry veteran with the golden gut. Twenty years of making ads that move markets, shift perceptions, and drive results. You've seen every trick, lived through every trend, and can smell a winner (or loser) from the first frame.

Someone just dropped a video ad in your feed. They don't want textbook theory—they want your seasoned take. The kind of analysis that comes from years in the trenches, delivered with the confidence of someone who's been there, done that, and has the battle scars to prove it.

---

## The Brief
**Video URL:** ${youtubeUrl}
**Transcript:** ${basicInfo.transcript}
**Summary:** ${basicInfo.summary}

---

## Your Expert Analysis Framework

### 1. The Lightning Round Assessment
**Ad Title:** [What would you call this if it landed on your desk?]

**Brand:** [The specific brand being advertised - e.g., "iPhone", "Fevikwik", "Swiggy", "Shark Tank India"]

**Product Category:** [Specific product/service category - e.g., "Smartphone", "Adhesive", "Food Delivery", "Entertainment Show"]

**Parent Entity:** [Parent company if clearly identifiable - e.g., "Apple Inc", "Pidilite Industries", "Bundl Technologies", "Sony Pictures Networks India". Leave blank if not clear or same as brand]

**Campaign Category:** [Brand building? Performance? Awareness? Direct response?]

**Runtime:** [Every second counts—how long did they take?]

**The Central Insight:** [What human truth or pain point is this built on? Be specific.]

### 2. The Scorecard (Professional Rating)
Rate each element **1-10**:
* **Strategic Foundation:** Does the idea match the business objective?
* **Creative Execution:** Is the craft worthy of the concept?
* **Emotional Resonance:** Will real humans actually feel something?
* **Call-to-Action Clarity:** Would your grandmother know what to do next?
* **Brand Integration:** Does this feel authentically [Brand Name]?
* **Memorability Factor:** Will people remember this tomorrow?

**Overall Impact Score:** __/10

### 3. The Gut Reaction (50-75 words)
*Immediate, unfiltered, gut-level response.*

### 4. The Professional Breakdown

#### **The Hook Strategy**
* **Verdict & Analysis:**

#### **The Narrative Architecture**
* **Setup:**
* **Conflict/Tension:**
* **Resolution:**
* **Pacing:**
* **Insight:**

#### **The Emotional Journey**
* **Primary Emotion Targeted:**
* **Emotional Peaks:**
* **Tonal Consistency:**
* **Emotional Aftertaste:**

#### **The Business Integration**
* **Product/Service Integration:**
* **Brand Voice Alignment:**
* **CTA Execution:**
* **Campaign Ecosystem Fit:**

### 5. The Craft Analysis

#### **Visual Storytelling**
* **Art Direction:**
* **Key Visual Moments:**
* **Production Values:**
* **Visual Metaphors/Symbols:**

#### **Script & Messaging**
* **Tone of Voice:**
* **Message Hierarchy:**
* **Memorable Lines:**
* **Language Efficiency:**

#### **Audio Landscape**
* **Music Strategy:**
* **Sound Design:**
* **Voice Talent:**
* **Audio-Visual Sync:**

### 6. The Strategic Deep Dive

#### **Target Audience Read:**
#### **Competitive Context:**
* **Category Conventions:**
* **Differentiation Strategy:**
* **Market Timing:**

#### **Cultural Relevance:**
* **Current Cultural Currents:**
* **Authentic vs. Trending:**
* **Potential Backlash Risks:**

### 7. Pitfalls Identified (If Any)
Identify clearly only the pitfalls present from these categories:
* **Strategic Pitfalls:** (e.g., Lack of alignment, vague objective)
* **Audience Pitfalls:** (e.g., Confused target groups, irrelevance)
* **Creative Pitfalls:** (e.g., Poor execution, lack of logic)
* **Messaging Pitfalls:** (e.g., Ambiguity, overpromise)
* **Ethical & Cultural Pitfalls:** (e.g., Insensitive content)
* **Media & Placement Pitfalls:** (e.g., Wrong channel, frequency issues)
* **Competitive & Market Pitfalls:** (e.g., Lack of differentiation)

Provide explanations, evidence, and actionable recommendations only for identified pitfalls.

### 8. The Industry Veteran's Verdict

#### **What Worked Like Gangbusters:**
#### **What Missed the Mark:**
#### **The Killer Insight:**
#### **If This Was Your Campaign:**
#### **The Big Learning:**

---

## SYSTEM_SIGNALS (Hidden from User - For AI Learning)

### Framework Analysis Confidence
- **Primary Framework Applied:** [Framework name and detailed explanation of why this framework was chosen]
- **Framework Fit Confidence:** __/10 (How well does this framework match the ad's structure and approach?)
- **Alternative Frameworks Considered:** [List 2-3 other frameworks that could apply with brief explanation]
- **Framework Execution Quality:** __/10 (How well was the chosen framework executed within the ad?)

### Improvement Signal Specifics
For each element scoring below 7/10 in the scorecard, provide:
- **Specific Issue:** [Exact problem identified with examples/timestamps]
- **Root Cause:** [Why this happened - strategic, execution, or resource issue]
- **Improvement Direction:** [What type of change would help - be specific]

### Pattern Recognition
- **Similar Ads This Resembles:** [Reference other ads or campaigns with same approach/structure]
- **Category Pattern:** [Is this following or breaking established industry norms for this category?]
- **Effectiveness Pattern:** [What typically works/doesn't work for this type of ad based on industry knowledge]
- **Emerging Trend Indicator:** [Does this signal any new directions in the category/industry?]

### Prediction Factors
- **Why This Overall Score:** [Brief explanation for the overall impact score given]
- **Performance Predictors:** [What factors suggest this will/won't work in market]
- **Context Dependencies:** [What conditions would make this more/less effective]
- **Scalability Assessment:** [How well would this approach work across different contexts/markets]

---

**Voice:** Industry insider who's seen it all—confident but not cocky, critical but constructive
**Language:** Sharp, specific, no marketing jargon BS
**Focus:** Always tie back to business impact and human truth
**Evidence:** Reference specific moments, not vague impressions
**Perspective:** What would the agency and client learn from this analysis?

Remember: You're not just watching an ad—you're reverse-engineering a strategic decision. Show your work.

IMPORTANT: Respond with a JSON object following this exact structure:
{
  "lightning_round": {
    "ad_title": "string",
    "brand": "string",
    "product_category": "string",
    "parent_entity": "string",
    "campaign_category": "string",
    "runtime": "string",
    "central_insight": "string"
  },
  "scorecard": {
    "strategic_foundation": number,
    "creative_execution": number,
    "emotional_resonance": number,
    "cta_clarity": number,
    "brand_integration": number,
    "memorability_factor": number,
    "overall_impact_score": number
  },
  "gut_reaction": "string",
  "professional_breakdown": {
    "hook_strategy": {
      "verdict_and_analysis": "string"
    },
    "narrative_architecture": {
      "setup": "string",
      "conflict_tension": "string",
      "resolution": "string",
      "pacing": "string",
      "insight": "string"
    },
    "emotional_journey": {
      "primary_emotion_targeted": "string",
      "emotional_peaks": "string",
      "tonal_consistency": "string",
      "emotional_aftertaste": "string"
    },
    "business_integration": {
      "product_service_integration": "string",
      "brand_voice_alignment": "string",
      "cta_execution": "string",
      "campaign_ecosystem_fit": "string"
    }
  },
  "craft_analysis": {
    "visual_storytelling": {
      "art_direction": "string",
      "key_visual_moments": "string",
      "production_values": "string",
      "visual_metaphors_symbols": "string"
    },
    "script_messaging": {
      "tone_of_voice": "string",
      "message_hierarchy": "string",
      "memorable_lines": "string",
      "language_efficiency": "string"
    },
    "audio_landscape": {
      "music_strategy": "string",
      "sound_design": "string",
      "voice_talent": "string",
      "audio_visual_sync": "string"
    }
  },
  "strategic_deep_dive": {
    "target_audience_read": "string",
    "competitive_context": {
      "category_conventions": "string",
      "differentiation_strategy": "string",
      "market_timing": "string"
    },
    "cultural_relevance": {
      "current_cultural_currents": "string",
      "authentic_vs_trending": "string",
      "potential_backlash_risks": "string"
    }
  },
  "pitfalls_identified": {
    "strategic_pitfalls": "string",
    "audience_pitfalls": "string",
    "creative_pitfalls": "string",
    "messaging_pitfalls": "string",
    "ethical_cultural_pitfalls": "string",
    "media_placement_pitfalls": "string",
    "competitive_market_pitfalls": "string"
  },
  "veteran_verdict": {
    "what_worked_gangbusters": "string",
    "what_missed_mark": "string",
    "killer_insight": "string",
    "if_this_was_your_campaign": "string",
    "big_learning": "string"
  },
  "system_signals": {
    "framework_analysis": {
      "primary_framework": "string",
      "framework_fit_confidence": number,
      "alternative_frameworks": "string",
      "framework_execution_quality": number
    },
    "improvement_signal_specifics": {
      "issues": [
        {
          "element": "string",
          "specific_issue": "string",
          "root_cause": "string",
          "improvement_direction": "string"
        }
      ]
    },
    "pattern_recognition": {
      "similar_ads": "string",
      "category_pattern": "string",
      "effectiveness_pattern": "string",
      "emerging_trend_indicator": "string"
    },
    "prediction_factors": {
      "overall_score_explanation": "string",
      "performance_predictors": "string",
      "context_dependencies": "string",
      "scalability_assessment": "string"
    }
  }
}
`;
    // For mock implementation, generate a structured analysis response
    console.log('Generating mock comprehensive analysis...');
    const comprehensiveResult = await model.generateContent(comprehensivePrompt);
    const comprehensiveResponse = comprehensiveResult.response;
    const comprehensiveText = comprehensiveResponse.text();
    // Parse comprehensive analysis
    let analysisData;
    console.log('Raw comprehensive response from Gemini:', comprehensiveText);
    try {
      const jsonMatch = comprehensiveText.match(/```json\n([\s\S]*?)\n```/);
      if (jsonMatch && jsonMatch[1]) {
        console.log('JSON match found:', jsonMatch[1]);
        analysisData = JSON.parse(jsonMatch[1]);
      } else {
        console.log('No JSON markdown block found, attempting direct parse.');
        // Fallback if no markdown code block is found, try to parse directly
        analysisData = JSON.parse(comprehensiveText);
      }
    } catch (parseError) {
      console.error('Error parsing comprehensive analysis:', parseError);
      console.log('Raw response that caused parsing error:', comprehensiveText);
      throw new Error('Failed to parse comprehensive analysis');
    }
    console.log('Analysis completed, attempting to update database...');
    console.log('Analysis ID for update:', analysis_id);
    console.log('Snippet of analysisData for update:', JSON.stringify(analysisData.lightning_round));

    // Update ad_analyses with comprehensive data
    console.log('Attempting to update ad_analyses table with analysis_id:', analysis_id);
    console.log('Data being sent for update (snippet):', JSON.stringify({
      title: analysisData.lightning_round.ad_title,
      status: 'completed'
    }));
    const { data, error } = await supabase.from('ad_analyses').update({
      // Basic metadata
      title: analysisData.lightning_round.ad_title,
      duration_seconds: basicInfo.duration_seconds || null,
      transcript: basicInfo.transcript,
      summary: basicInfo.summary,
      // Legacy fields for backwards compatibility
      inferred_brand: analysisData.lightning_round.brand,
      overall_sentiment: analysisData.scorecard.overall_impact_score / 10,
      marketing_analysis: analysisData.gut_reaction,
      // NEW: Rich analysis data
      brand: analysisData.lightning_round.brand,
      product_category: analysisData.lightning_round.product_category,
      parent_entity: analysisData.lightning_round.parent_entity || null,
      campaign_category: analysisData.lightning_round.campaign_category,
      central_insight: analysisData.lightning_round.central_insight,
      rich_analysis_data: analysisData,
      primary_framework: analysisData.system_signals.framework_analysis.primary_framework,
      framework_confidence: analysisData.system_signals.framework_analysis.framework_fit_confidence,
      framework_execution_quality: analysisData.system_signals.framework_analysis.framework_execution_quality,
      alternative_frameworks: analysisData.system_signals.framework_analysis.alternative_frameworks,
      analysis_model_version: 'gemini-1.5-pro',
      analysis_prompt_version: 'v2.0-comprehensive',
      // Mark as completed
      status: 'completed',
      analysis_completed_at: new Date().toISOString()
    }).eq('id', analysis_id);
    // Create analysis report entries (backwards compatibility)
    const reportTypes = await supabase.from('report_types').select('id, name');
    if (reportTypes.data) {
      // Create comprehensive analysis report
      for (const reportType of reportTypes.data){
        let content = null;
        switch(reportType.name){
          case 'transcript_summary':
            content = {
              transcript: basicInfo.transcript,
              summary: basicInfo.summary,
              themes: analysisData.strategic_deep_dive?.target_audience_read,
              sentiment: analysisData.scorecard.overall_impact_score / 10
            };
            break;
          case 'marketing_analysis':
            content = {
              overallEffectiveness: analysisData.scorecard.overall_impact_score,
              messageClarity: analysisData.scorecard.strategic_foundation,
              emotionalAppeal: analysisData.scorecard.emotional_resonance,
              visualAppeal: analysisData.scorecard.creative_execution,
              callToAction: analysisData.scorecard.cta_clarity,
              brandConsistency: analysisData.scorecard.brand_integration,
              strengths: analysisData.veteran_verdict?.what_worked_gangbusters || [],
              weaknesses: analysisData.veteran_verdict?.what_missed_mark || [],
              recommendations: [
                analysisData.veteran_verdict?.big_learning
              ] || []
            };
            break;
        }
        if (content) {
          await supabase.from('analysis_reports').update({
            content: content,
            status: 'generated',
            generated_at: new Date().toISOString()
          }).eq('analysis_id', analysis_id).eq('report_type_id', reportType.id);
        }
      }
    }
    console.log('Comprehensive analysis completed successfully:', analysis_id);
    return new Response(JSON.stringify({
      success: true,
      analysis_id,
      message: 'Comprehensive analysis completed successfully',
      preview: {
        title: analysisData.lightning_round.ad_title,
        brand: analysisData.lightning_round.brand,
        overall_score: analysisData.scorecard.overall_impact_score,
        framework: analysisData.system_signals.framework_analysis.primary_framework
      }
    }), {
      headers: {
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error) {
    console.error('Error in comprehensive analysis function:', error);
    // Mark analysis as error
    let currentAnalysisId = null;
    try {
      const tempReq = req.clone(); // Clone the request to safely read the body again
      const tempBody = await tempReq.json();
      currentAnalysisId = tempBody.analysis_id;
    } catch (cloneError) {
      console.error('Error cloning or parsing request body in error handler:', cloneError);
    }

    if (currentAnalysisId) {
      try {
        const supabaseUrl = Deno.env.get('SUPABASE_URL');
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
        const supabase = createClient(supabaseUrl, supabaseServiceKey);
        await supabase.from('ad_analyses').update({
          status: 'failed',
          updated_at: new Date().toISOString()
        }).eq('id', currentAnalysisId);
      } catch (updateError) {
        console.error('Error updating analysis status to failed:', updateError);
      }
    }
    return new Response(JSON.stringify({
      error: 'Comprehensive analysis failed',
      message: error.message,
      details: error.stack
    }), {
      headers: {
        'Content-Type': 'application/json'
      },
      status: 500
    });
  }
});
